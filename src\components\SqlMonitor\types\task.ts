/**
 * 任务相关类型定义
 */

// 主页表格展示数据 基本信息 页面加载时展示的数据
// 表格不展示 id alert_task_id alert_send_id other_info_id db_connection_id
export interface TaskBasic {
  // 主键
  id: number;
  // 任务名称：唯一
  name: string;
  // 任务分组：非多选
  group_id: string;
  group_name: string;
  /** 状态 */
  status: 'enabled' | 'disabled';
  // 开始时间, 格式为HH:mm:ss
  start_time: string;
  // 结束时间, 格式为HH:mm:ss
  end_time: string;
  // 星期，格式 1-7 / 1,2,3,4,5
  // 表格展示格式：1,2,3,4,5
  // 抽屉编辑：转换为中文星期几，提交时转换回数字格式
  weekday: string[];
  // 执行频率(间隔)：
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分/时/日
  frequency: { value: number; unit: string };
  // 重试次数
  retry_num: string;
  // 重试间隔
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分钟/小时
  retry_frequency: { value: number; unit: string };
  // 告警触发条件
  alert_task_id: string[];
  // 告警发送方式
  alert_send_id: string[];
  // db 连接id
  db_connection_id: string;
  // 附加信息id
  other_info_id: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 后端查询返回数据结构
export type TaskBasicFormData = {
  id: number;
  // 任务名称：唯一
  name: string;
  // 任务分组：非多选
  group_id: string;
  group_name: string;
  /** 状态 */
  status: string;
  // 开始时间, 格式为HH:mm:ss
  start_time: string;
  // 结束时间, 格式为HH:mm:ss
  end_time: string;
  // 星期，格式 1-7 / 1,2,3,4,5
  // 表格展示格式：1,2,3,4,5
  // 抽屉编辑：转换为中文星期几，提交时转换回数字格式
  weekday: string;
  // 执行频率(间隔)：
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分/时/日
  frequency: string;
  // 重试次数
  retry_num: string;
  // 重试间隔
  // 表格展示格式：40sec/5hour
  // 抽屉编辑：数字：xxx, 单位：秒/分钟/小时
  retry_frequency: string;
  // 告警触发条件
  alert_task_id: string;
  // 告警发送方式
  alert_send_id: string;
  // db 连接id
  db_connection_id: string;
  // 附加信息id
  other_info_id: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
};

// 抽屉内 基本信息 表单数据
export type TaskBasicFormDataAdd = Omit<TaskBasicFormData, 'id' | 'create_time' | 'update_time'>;
export type TaskBasicFormDataUpdateOrDelete = Omit<TaskBasicFormData, 'create_time' | 'update_time'>;

// 提交给后端的数据结构
export type TaskBasicFormSubmitData = TaskBasicFormDataAdd | TaskBasicFormDataUpdateOrDelete;

// 任务分组数据结构
export type TaskBasicGroupFormData = {
  id: number;
  name: string;
  is_used: boolean;
  create_time: string;
  update_time: string;
};

// 抽屉内 分组 表单数据
export type TaskBasicGroupFormDataAdd = Omit<TaskBasicGroupFormData, 'id' | 'create_time' | 'update_time'>;
export type TaskBasicGroupFormDataUpdate = Omit<TaskBasicGroupFormData, 'create_time' | 'update_time'>;

// 告警配置，基本信息包含多个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息alert_task_id查询数据, table data, 页面不展示id
export interface TaskAlert {
  // 主键
  id: number;
  // 唯一
  name: string;
  // 告警级别
  severity: string;
  // 告警对象：执行SQL结果
  sql: string;
  // 告警类型: isExist/isEqual
  type: string;
  // 触发值：isEqual时  针对SQL返回值判断; isExist时 values 为空
  values: string[];
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 告警配置 表单数据
export type TaskAlertFormAdd = Omit<TaskAlert, 'id' | 'create_time' | 'update_time'>;
export type TaskAlertFormUpdateOrDelete = Omit<TaskAlert, 'create_time' | 'update_time'>;

// 数据库配置， 基本信息包含单个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息db_connection_id查询数据
export interface DBConnection {
  id: number;
  // 唯一
  name: string;
  // 数据库类型：mysql/oracle
  db_type: string;
  // 数据库连接信息
  host: string;
  // 端口
  port: string;
  // 用户名
  user: string;
  // base64 简单加密
  passwd: string;
  // mysql 独有
  database: string;
  use_ssl: boolean;
  server_timezone: string;
  // oracle 独有
  instance: string;
  // oracle 独有 sid/service
  connect_method: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 DB配置 表单数据
export type DBFormAdd = Omit<DBConnection, 'id' | 'create_time' | 'update_time'>;
export type DBFormUpdateOrDelete = Omit<DBConnection, 'create_time' | 'update_time'>;

// 告警发送配置， 基本信息包含多个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息alert_send_id查询数据
export interface AlertSend {
  id: number;
  // 唯一
  name: string;
  // 接收类型：kafka/prometheus
  type: string;
  // kafka 独有
  kafka_receiver: KafkaReceiver;
  // prometheus 独有
  prometheus_receiver: PrometheusReceiver;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// prometheus地址
export interface PrometheusReceiver {
  // 连接地址 格式：***********:9090,***********:9090,
  address: string;
  username: string;
  password: string;
}

export interface KafkaReceiver {
  // 连接地址 格式：***********:9090,***********:9090,
  address: string;
  username: string;
  password: string;
  topic: string;
}

// 抽屉内 告警发送配置 表单数据
export type AlertSendFormAdd = Omit<AlertSend, 'id' | 'create_time' | 'update_time'>;
export type AlertSendFormUpdateOrDelete = Omit<AlertSend, 'create_time' | 'update_time'>;

// 附加信息配置， 基本信息包含单个告警配置
// 位于抽屉内，可新增/编辑/删除
// 打开抽屉时，使用基本信息other_info_id查询数据
export interface OtherInfo {
  id: number;
  // 唯一
  name: string;
  // 业务系统名称
  business: string;
  // 业务系统英文名称
  business_en: string;
  // 主机名称
  hostname: string;
  // 告警来源
  location: string;
  // 创建日期
  create_time: string;
  // 更新日期
  update_time: string;
}

// 抽屉内 其他信息配置 表单数据
export type OtherFormAdd = Omit<OtherInfo, 'id' | 'create_time' | 'update_time'>;
export type OtherFormUpdateOrDelete = Omit<OtherInfo, 'create_time' | 'update_time'>;

/**
 * 主表搜索参数类型
 */
export type TaskBasicSearchParams = {
  current?: number;
  page_size?: number;
  name?: string;
  group_name?: string;
  status?: 'enabled' | 'disabled';
  weekday?: string;
  frequency?: string;
  start_time?: string;
  end_time?: string;
  retry_num?: string;
  retry_frequency?: string;
  alert_receiver?: string;
  group?: string;
};

/**
 * 任务分组搜索参数类型
 */
export type TaskGroupSearchParams = {
  current?: number;
  page_size?: number;
  name?: string;
  is_used?: boolean;
};
