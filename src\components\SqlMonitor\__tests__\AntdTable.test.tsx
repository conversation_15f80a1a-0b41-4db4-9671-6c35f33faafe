import React from 'react';
import { render, screen } from '@testing-library/react';
import { App } from 'antd';
import AntdTable from '../AntdTable';

// Mock the hooks and services
jest.mock('../../hooks', () => ({
  useTaskData: () => ({
    data: [],
    loading: false,
    total: 0,
    pagination: { current: 1, page_size: 10 },
    searchParams: {},
    loadData: jest.fn(),
    refreshData: jest.fn(),
    resetData: jest.fn(),
    updateSearchParams: jest.fn(),
    updatePagination: jest.fn(),
  }),
  useTaskSelection: () => ({
    allSelectedRows: new Map(),
    rowSelection: {},
    clearSelection: jest.fn(),
    getSelectedCount: () => 0,
  }),
  useTable: () => ({
    tableScrollY: 400,
    filteredInfo: {},
    handleTableChange: jest.fn(),
    getSortOrder: () => null,
    resetSortAndFilter: jest.fn(),
  }),
  useModal: () => ({
    modalState: { visible: false },
    showModal: jest.fn(),
    hideModal: jest.fn(),
  }),
  useDrawer: () => ({
    drawerState: { visible: false },
    showDrawer: jest.fn(),
    hideDrawer: jest.fn(),
  }),
}));

jest.mock('../../services', () => ({
  TaskService: {
    deleteTask: jest.fn(),
    batchDeleteTasks: jest.fn(),
  },
}));

// Mock the child components
jest.mock('../TableColumns', () => ({
  createTableColumns: () => [],
}));

jest.mock('../SearchForm', () => ({
  QuickSearchForm: () => <div data-testid='quick-search-form'>Quick Search Form</div>,
}));

jest.mock('../ActionButtons', () => ({
  ActionButtons: () => <div data-testid='action-buttons'>Action Buttons</div>,
}));

jest.mock('../TaskTable', () => ({
  TaskTable: () => <div data-testid='task-table'>Task Table</div>,
}));

jest.mock('../ModalManager', () => ({
  ModalManager: () => <div data-testid='modal-manager'>Modal Manager</div>,
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <App>{children}</App>
);

describe('AntdTable', () => {
  it('应该正确渲染所有子组件', () => {
    render(
      <TestWrapper>
        <AntdTable contentHeight={600} />
      </TestWrapper>
    );

    // 验证所有子组件都被渲染
    expect(screen.getByTestId('quick-search-form')).toBeInTheDocument();
    expect(screen.getByTestId('action-buttons')).toBeInTheDocument();
    expect(screen.getByTestId('task-table')).toBeInTheDocument();
    expect(screen.getByTestId('modal-manager')).toBeInTheDocument();
  });

  it('应该有正确的布局结构', () => {
    render(
      <TestWrapper>
        <AntdTable />
      </TestWrapper>
    );

    // 验证主容器存在
    const mainContainer = document.querySelector('.h-full.flex.flex-col');
    expect(mainContainer).toBeInTheDocument();
  });
});
