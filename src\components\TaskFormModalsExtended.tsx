import React, { useEffect } from 'react';
import { Modal, Form, Input, Button, Row, Col, Table, Space, Tag, Tooltip, App } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { OtherInfo, TaskAlert, AlertSend, DBConnection } from '../types/task';
import styles from './TaskFormModalsExtended.module.css';

// 联合类型用于 SelectModal
type SelectableData = TaskAlert | AlertSend | DBConnection | OtherInfo;

interface OtherInfoModalProps {
  visible: boolean;
  editingData?: OtherInfo;
  onCancel: () => void;
  onSubmit: (data: OtherInfo) => void;
}

interface SelectModalProps {
  visible: boolean;
  type: 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
  data: SelectableData[];
  selectedData?: SelectableData[]; // 已选择的数据，用于过滤
  onCancel: () => void;
  onSubmit: (selectedItems: SelectableData[]) => void;
  onSearch?: (searchText: string) => void; // 搜索回调函数
  multiple?: boolean;
}

// 其他信息Modal
export const OtherInfoModal: React.FC<OtherInfoModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const otherInfoData: OtherInfo = {
      id: editingData?.id || Date.now(),
      ...values,
    };
    onSubmit(otherInfoData);
  };

  return (
    <Modal
      title={editingData ? '编辑其他信息' : '新增其他信息'}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='信息名称'
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入信息名称',
                },
              ]}
            >
              <Input
                placeholder='请输入信息名称'
                autoComplete='off'
                autoCorrect='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='业务系统名称'
              name='business'
              rules={[
                {
                  required: true,
                  message: '请输入业务系统名称',
                },
              ]}
            >
              <Input
                placeholder='请输入业务系统名称'
                autoComplete='off'
                autoCorrect='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='业务系统英文名称'
              name='businessEn'
              rules={[
                {
                  required: true,
                  message: '请输入业务系统英文名称',
                },
              ]}
            >
              <Input
                placeholder='请输入业务系统英文名称'
                autoComplete='off'
                autoCorrect='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='主机名称'
              name='hostname'
              rules={[
                {
                  required: true,
                  message: '请输入主机名称',
                },
              ]}
            >
              <Input
                placeholder='请输入主机名称'
                autoComplete='off'
                autoCorrect='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label='告警来源'
          name='location'
          rules={[
            {
              required: true,
              message: '请输入告警来源',
            },
          ]}
        >
          <Input
            placeholder='请输入告警来源'
            autoComplete='off'
            autoCorrect='off'
            autoCapitalize='off'
            spellCheck={false}
          />
        </Form.Item>

        <div className='flex justify-end space-x-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 选择已有数据Modal
export const SelectModal: React.FC<SelectModalProps> = ({
  visible,
  type,
  data,
  selectedData = [],
  onCancel,
  onSubmit,
  onSearch,
  multiple = true,
}) => {
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<any[]>([]);
  const [pagination, setPagination] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchText, setSearchText] = React.useState<string>('');
  // 数据库连接的多字段搜索
  const [dbSearchFields, setDbSearchFields] = React.useState({
    name: '',
    dbType: '',
    host: '',
  });
  // 告警发送的双字段搜索
  const [alertSendSearchFields, setAlertSendSearchFields] = React.useState({
    name: '',
    type: '',
  });

  // 过滤掉已选择的数据
  const filteredData = React.useMemo(() => {
    let result = data;

    // 过滤掉已选择的数据
    if (selectedData && selectedData.length > 0) {
      const selectedIds = selectedData.map(item => item.id);
      result = result.filter(item => !selectedIds.includes(item.id));
    }

    return result;
  }, [data, selectedData]);

  // 更新分页总数
  React.useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: filteredData.length,
      current: 1, // 重置到第一页
    }));
  }, [filteredData]);

  const getColumns = () => {
    switch (type) {
      case 'alert':
        return [
          {
            title: '告警名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <span className={styles.alertName || 'alert-name'}>{text}</span>
              </Tooltip>
            ),
            sorter: (a: any, b: any) => a.name.localeCompare(b.name),
          },
          {
            title: '告警级别',
            dataIndex: 'severity',
            key: 'severity',
            width: 100,
            align: 'center' as const,
            render: (severity: string) => {
              const severityConfig = {
                low: {
                  color: 'green',
                  text: '低',
                },
                medium: {
                  color: 'orange',
                  text: '中',
                },
                high: {
                  color: 'red',
                  text: '高',
                },
                critical: {
                  color: 'volcano',
                  text: '紧急',
                },
              };
              const config = severityConfig[severity as keyof typeof severityConfig] || {
                color: 'default',
                text: severity,
              };
              return (
                <Tag color={config.color} className={styles.severityTag || 'severity-tag'}>
                  {config.text}
                </Tag>
              );
            },
            filters: [
              {
                text: '低',
                value: 'low',
              },
              {
                text: '中',
                value: 'medium',
              },
              {
                text: '高',
                value: 'high',
              },
              {
                text: '紧急',
                value: 'critical',
              },
            ],
            onFilter: (value: any, record: any) => record.severity === value,
          },
          {
            title: '告警类型',
            dataIndex: 'type',
            key: 'type',
            width: 120,
            align: 'center' as const,
            render: (type: string) => {
              const typeConfig = {
                isExist: {
                  color: 'blue',
                  text: '存在检查',
                },
                isEqual: {
                  color: 'purple',
                  text: '值比较',
                },
              };
              const config = typeConfig[type as keyof typeof typeConfig] || {
                color: 'default',
                text: type,
              };
              return (
                <Tag color={config.color} className={styles.typeTag || 'type-tag'}>
                  {config.text}
                </Tag>
              );
            },
            filters: [
              {
                text: '存在检查',
                value: 'isExist',
              },
              {
                text: '值比较',
                value: 'isEqual',
              },
            ],
            onFilter: (value: any, record: any) => record.type === value,
          },
          {
            title: 'SQL语句',
            dataIndex: 'sql',
            key: 'sql',
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <code className={styles.sqlCode || 'sql-code'}>{text}</code>
              </Tooltip>
            ),
          },
          {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            width: 150,
            align: 'center' as const,
            render: (text: string) => (
              <span className={styles.timeText || 'time-text'}>
                {text
                  ? new Date(text).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : '-'}
              </span>
            ),
            sorter: (a: any, b: any) =>
              new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
          },
        ];
      case 'alertSend':
        return [
          {
            title: '发送名称',
            dataIndex: 'name',
            key: 'name',
            width: 180,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <span className={styles.alertName || 'alert-name'}>{text}</span>
              </Tooltip>
            ),
            sorter: (a: any, b: any) => a.name.localeCompare(b.name),
          },
          {
            title: '发送类型',
            dataIndex: 'type',
            key: 'type',
            width: 120,
            align: 'center' as const,
            render: (type: string) => {
              const typeConfig = {
                kafka: {
                  color: 'green',
                  text: 'Kafka',
                },
                prometheus: {
                  color: 'purple',
                  text: 'Prometheus',
                },
              };
              const config = typeConfig[type as keyof typeof typeConfig] || {
                color: 'default',
                text: type,
              };
              return (
                <Tag color={config.color} className={styles.typeTag || 'type-tag'}>
                  {config.text}
                </Tag>
              );
            },
            filters: [
              {
                text: 'Kafka',
                value: 'kafka',
              },
              {
                text: 'Prometheus',
                value: 'prometheus',
              },
            ],
            onFilter: (value: any, record: any) => record.type === value,
          },
          {
            title: '接收地址',
            key: 'address',
            width: 250,
            ellipsis: {
              showTitle: false,
            },
            render: (_: string, record: AlertSend) => {
              let address = '';
              if (record.type === 'kafka' && record.kafka_receiver?.address) {
                address = record.kafka_receiver.address;
              } else if (record.type === 'prometheus' && record.prometheus_receiver?.address) {
                address = record.prometheus_receiver.address;
              }
              return (
                <Tooltip placement='topLeft' title={address || '无'}>
                  <code
                    className={styles.sqlCode || 'sql-code'}
                    style={{
                      backgroundColor: '#f0f2f5',
                    }}
                  >
                    {address || '无'}
                  </code>
                </Tooltip>
              );
            },
          },
          {
            title: '用户名',
            key: 'username',
            width: 120,
            ellipsis: {
              showTitle: false,
            },
            render: (_: string, record: AlertSend) => {
              let username = '';
              if (record.type === 'kafka' && record.kafka_receiver?.username) {
                username = record.kafka_receiver.username;
              } else if (record.type === 'prometheus' && record.prometheus_receiver?.username) {
                username = record.prometheus_receiver.username;
              }
              return (
                <Tooltip placement='topLeft' title={username || '无'}>
                  <span
                    style={{
                      color: username ? '#666' : '#ccc',
                      fontStyle: username ? 'normal' : 'italic',
                    }}
                  >
                    {username || '无'}
                  </span>
                </Tooltip>
              );
            },
          },
          {
            title: 'Topic/路径',
            key: 'topic',
            width: 150,
            ellipsis: {
              showTitle: false,
            },
            render: (_: string, record: AlertSend) => {
              let topic = '';
              if (record.type === 'kafka' && record.kafka_receiver?.topic) {
                topic = record.kafka_receiver.topic;
              }
              return (
                <Tooltip placement='topLeft' title={topic || '无'}>
                  <span
                    style={{
                      color: topic ? '#333' : '#ccc',
                      fontStyle: topic ? 'normal' : 'italic',
                      fontSize: '12px',
                    }}
                  >
                    {topic || '无'}
                  </span>
                </Tooltip>
              );
            },
          },
          {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            width: 150,
            align: 'center' as const,
            render: (text: string) => (
              <span className={styles.timeText || 'time-text'}>
                {text
                  ? new Date(text).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : '-'}
              </span>
            ),
            sorter: (a: any, b: any) =>
              new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
          },
        ];
      case 'dbConnection':
        return [
          {
            title: '连接名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <span className={styles.alertName || 'alert-name'}>{text}</span>
              </Tooltip>
            ),
            sorter: (a: any, b: any) => a.name.localeCompare(b.name),
          },
          {
            title: '数据库类型',
            dataIndex: 'db_type',
            key: 'db_type',
            width: 120,
            align: 'center' as const,
            render: (dbType: string) => {
              const typeConfig = {
                mysql: {
                  color: 'blue',
                  text: 'MySQL',
                },
                oracle: {
                  color: 'orange',
                  text: 'Oracle',
                },
                postgresql: {
                  color: 'green',
                  text: 'PostgreSQL',
                },
                sqlserver: {
                  color: 'purple',
                  text: 'SQL Server',
                },
              };
              const config = typeConfig[dbType as keyof typeof typeConfig] || {
                color: 'default',
                text: dbType,
              };
              return (
                <Tag color={config.color} className={styles.typeTag || 'type-tag'}>
                  {config.text}
                </Tag>
              );
            },
            filters: [
              {
                text: 'MySQL',
                value: 'mysql',
              },
              {
                text: 'Oracle',
                value: 'oracle',
              },
              {
                text: 'PostgreSQL',
                value: 'postgresql',
              },
              {
                text: 'SQL Server',
                value: 'sqlserver',
              },
            ],
            onFilter: (value: any, record: any) => record.db_type === value,
          },
          {
            title: '主机地址',
            dataIndex: 'host',
            key: 'host',
            width: 150,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <code
                  className={styles.sqlCode || 'sql-code'}
                  style={{
                    backgroundColor: '#f0f2f5',
                  }}
                >
                  {text}
                </code>
              </Tooltip>
            ),
          },
          {
            title: '端口',
            dataIndex: 'port',
            key: 'port',
            width: 80,
            align: 'center' as const,
            render: (text: string) => (
              <span
                style={{
                  fontFamily: 'Monaco, Menlo, monospace',
                  fontSize: '12px',
                  color: '#666',
                }}
              >
                {text}
              </span>
            ),
          },
          {
            title: '用户名',
            dataIndex: 'user',
            key: 'user',
            width: 120,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <span
                  style={{
                    color: '#666',
                  }}
                >
                  {text}
                </span>
              </Tooltip>
            ),
          },
          {
            title: '数据库名',
            dataIndex: 'database',
            key: 'database',
            width: 120,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text || '无'}>
                <span
                  style={{
                    color: text ? '#333' : '#ccc',
                    fontStyle: text ? 'normal' : 'italic',
                  }}
                >
                  {text || '无'}
                </span>
              </Tooltip>
            ),
          },
          {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            width: 150,
            align: 'center' as const,
            render: (text: string) => (
              <span className={styles.timeText || 'time-text'}>
                {text
                  ? new Date(text).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : '-'}
              </span>
            ),
            sorter: (a: any, b: any) =>
              new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
          },
        ];
      case 'otherInfo':
        return [
          {
            title: '信息名称',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <span className={styles.alertName || 'alert-name'}>{text}</span>
              </Tooltip>
            ),
            sorter: (a: any, b: any) => a.name.localeCompare(b.name),
          },
          {
            title: '业务系统',
            dataIndex: 'business',
            key: 'business',
            width: 150,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <Tag color='blue' className={styles.typeTag || 'type-tag'}>
                  {text}
                </Tag>
              </Tooltip>
            ),
            filters: [
              {
                text: '用户管理系统',
                value: '用户管理系统',
              },
              {
                text: '订单管理系统',
                value: '订单管理系统',
              },
              {
                text: '商品管理系统',
                value: '商品管理系统',
              },
              {
                text: '库存管理系统',
                value: '库存管理系统',
              },
              {
                text: '支付系统',
                value: '支付系统',
              },
              {
                text: '物流系统',
                value: '物流系统',
              },
              {
                text: '客服系统',
                value: '客服系统',
              },
              {
                text: '数据分析系统',
                value: '数据分析系统',
              },
              {
                text: '监控系统',
                value: '监控系统',
              },
              {
                text: '日志系统',
                value: '日志系统',
              },
            ],
            onFilter: (value: any, record: any) => record.business === value,
          },
          {
            title: '英文名称',
            dataIndex: 'business_en',
            key: 'business_en',
            width: 150,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <code
                  className={styles.sqlCode || 'sql-code'}
                  style={{
                    backgroundColor: '#f0f2f5',
                  }}
                >
                  {text}
                </code>
              </Tooltip>
            ),
          },
          {
            title: '主机名称',
            dataIndex: 'hostname',
            key: 'hostname',
            width: 180,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => (
              <Tooltip placement='topLeft' title={text}>
                <code
                  className={styles.sqlCode || 'sql-code'}
                  style={{
                    backgroundColor: '#f6f8fa',
                  }}
                >
                  {text}
                </code>
              </Tooltip>
            ),
          },
          {
            title: '告警来源',
            dataIndex: 'location',
            key: 'location',
            width: 150,
            ellipsis: {
              showTitle: false,
            },
            render: (text: string) => {
              const locationConfig = {
                北京机房: {
                  color: 'red',
                },
                上海机房: {
                  color: 'blue',
                },
                广州机房: {
                  color: 'green',
                },
                深圳机房: {
                  color: 'orange',
                },
                杭州机房: {
                  color: 'purple',
                },
                成都机房: {
                  color: 'cyan',
                },
                武汉机房: {
                  color: 'magenta',
                },
                西安机房: {
                  color: 'volcano',
                },
                南京机房: {
                  color: 'geekblue',
                },
                青岛机房: {
                  color: 'gold',
                },
              };
              const config = locationConfig[text as keyof typeof locationConfig] || {
                color: 'default',
              };
              return (
                <Tooltip placement='topLeft' title={text}>
                  <Tag color={config.color} className={styles.typeTag || 'type-tag'}>
                    {text}
                  </Tag>
                </Tooltip>
              );
            },
            filters: [
              {
                text: '北京机房',
                value: '北京机房',
              },
              {
                text: '上海机房',
                value: '上海机房',
              },
              {
                text: '广州机房',
                value: '广州机房',
              },
              {
                text: '深圳机房',
                value: '深圳机房',
              },
              {
                text: '杭州机房',
                value: '杭州机房',
              },
              {
                text: '阿里云华东1',
                value: '阿里云华东1',
              },
              {
                text: '阿里云华北2',
                value: '阿里云华北2',
              },
              {
                text: '腾讯云上海',
                value: '腾讯云上海',
              },
              {
                text: '腾讯云北京',
                value: '腾讯云北京',
              },
            ],
            onFilter: (value: any, record: any) => record.location === value,
          },
          {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            width: 150,
            align: 'center' as const,
            render: (text: string) => (
              <span className={styles.timeText || 'time-text'}>
                {text
                  ? new Date(text).toLocaleString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })
                  : '-'}
              </span>
            ),
            sorter: (a: any, b: any) =>
              new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
          },
        ];
      default:
        return [];
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'alert':
        return '选择告警';
      case 'alertSend':
        return '选择联系方式';
      case 'dbConnection':
        return '选择连接';
      case 'otherInfo':
        return '选择附加信息';
      default:
        return '选择数据';
    }
  };

  const handleSubmit = () => {
    if (selectedRows.length === 0) {
      const typeText =
        type === 'alert'
          ? '告警'
          : type === 'alertSend'
            ? '联系方式'
            : type === 'dbConnection'
              ? '连接'
              : '附加信息';
      message.warning(`请选择${typeText}`);
      return;
    }
    onSubmit(selectedRows);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    // 重置分页状态
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
  };

  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setSearchText('');
    setDbSearchFields({
      name: '',
      dbType: '',
      host: '',
    });
    setAlertSendSearchFields({
      name: '',
      type: '',
    });
    // 重置分页状态
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    onCancel();
  };

  // 重置选择
  const handleResetSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  // 处理搜索
  const handleSearch = () => {
    if (type === 'dbConnection') {
      // 数据库连接的多字段搜索
      const { name, dbType, host } = dbSearchFields;
      if (!name.trim() && !dbType.trim() && !host.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(
          JSON.stringify({
            name: name.trim(),
            dbType: dbType.trim(),
            host: host.trim(),
          })
        );
      }
    } else if (type === 'alertSend') {
      // 告警发送的双字段搜索
      const { name, type: sendType } = alertSendSearchFields;
      if (!name.trim() && !sendType.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(
          JSON.stringify({
            name: name.trim(),
            type: sendType.trim(),
          })
        );
      }
    } else {
      // 其他类型的单字段搜索（告警、其他信息等）
      if (!searchText.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(searchText.trim());
      }
    }
  };

  // 获取搜索框占位符文本
  const getSearchPlaceholder = () => {
    switch (type) {
      case 'alert':
        return '请输入告警名称';
      case 'dbConnection':
        return '请输入连接名称/数据库类型/主机地址';
      case 'alertSend':
        return '请输入发送名称';
      case 'otherInfo':
        return '请输入信息名称';
      default:
        return '请输入搜索内容';
    }
  };

  // 重置搜索
  const handleResetSearch = () => {
    if (type === 'dbConnection') {
      const { name, dbType, host } = dbSearchFields;
      if (!name.trim() && !dbType.trim() && !host.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setDbSearchFields({
        name: '',
        dbType: '',
        host: '',
      });
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    } else if (type === 'alertSend') {
      const { name, type: sendType } = alertSendSearchFields;
      if (!name.trim() && !sendType.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setAlertSendSearchFields({
        name: '',
        type: '',
      });
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    } else {
      // 其他类型的单字段重置（告警、其他信息等）
      if (!searchText.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setSearchText('');
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    }
  };

  // 处理分页变化
  const handleTableChange = (paginationConfig: any) => {
    setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
      total: filteredData.length,
    });
  };

  const rowSelection = {
    type: multiple ? 'checkbox' : 'radio',
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: any[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };

  return (
    <Modal
      title={getTitle()}
      open={visible}
      onCancel={handleCancel}
      maskClosable={false} // 点击遮蔽区域不退出
      footer={
        <div className='flex justify-end'>
          <Space size='middle'>
            <Button onClick={handleCancel}>取消</Button>
            <Button onClick={handleResetSelection}>重置选择</Button>
            <Button type='primary' onClick={handleSubmit}>
              确认选择 ({selectedRows.length})
            </Button>
          </Space>
        </div>
      }
      width={1200} // 增加Modal宽度以适应更多列
      styles={{
        body: {
          padding: '16px 24px',
        },
      }}
    >
      {/* 搜索框和搜索按钮 */}
      <div className='mb-4'>
        {(type === 'alert' || type === 'otherInfo') && (
          <div className='flex justify-between items-center'>
            <div className='flex items-center'>
              <Space>
                <Input
                  placeholder={getSearchPlaceholder()}
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  style={{
                    width: type === 'otherInfo' ? 250 : 280,
                  }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </Space>
            </div>
            <div>
              <Space>
                <Button
                  type='primary'
                  onClick={handleSearch}
                  className={styles.searchButton || 'search-button'}
                >
                  搜索
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleResetSearch}
                  className={styles.resetButton || 'reset-button'}
                >
                  重置搜索
                </Button>
              </Space>
            </div>
          </div>
        )}

        {type === 'dbConnection' && (
          <div>
            <div className='flex justify-between items-center mb-3'>
              <div className='flex items-center gap-3'>
                <Input
                  placeholder='连接名称'
                  prefix={<SearchOutlined />}
                  value={dbSearchFields.name}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  style={{ width: 180 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
                <Input
                  placeholder='数据库类型'
                  value={dbSearchFields.dbType}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      dbType: e.target.value,
                    }))
                  }
                  style={{ width: 120 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
                <Input
                  placeholder='主机地址'
                  value={dbSearchFields.host}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      host: e.target.value,
                    }))
                  }
                  style={{ width: 150 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </div>
              <div>
                <Space>
                  <Button
                    type='primary'
                    onClick={handleSearch}
                    className={styles.searchButton || 'search-button'}
                  >
                    搜索
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetSearch}
                    className={styles.resetButton || 'reset-button'}
                  >
                    重置搜索
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        )}

        {type === 'alertSend' && (
          <div>
            <div className='flex justify-between items-center mb-3'>
              <div className='flex items-center gap-3'>
                <Input
                  placeholder='发送名称'
                  prefix={<SearchOutlined />}
                  value={alertSendSearchFields.name}
                  onChange={e =>
                    setAlertSendSearchFields(prev => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  style={{ width: 200 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
                <Input
                  placeholder='发送类型'
                  value={alertSendSearchFields.type}
                  onChange={e =>
                    setAlertSendSearchFields(prev => ({
                      ...prev,
                      type: e.target.value,
                    }))
                  }
                  style={{ width: 150 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete='off'
                  autoCorrect='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </div>
              <div>
                <Space>
                  <Button
                    type='primary'
                    onClick={handleSearch}
                    className={styles.searchButton || 'search-button'}
                  >
                    搜索
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetSearch}
                    className={styles.resetButton || 'reset-button'}
                  >
                    重置搜索
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        )}
      </div>

      {selectedData && selectedData.length > 0 && (
        <div className={styles.infoBanner || 'info-banner'}>
          <div className={styles.infoText || 'info-text'}>
            <span
              style={{
                fontWeight: 600,
              }}
            >
              提示：
            </span>
            已过滤掉 {selectedData.length} 个已选择的项目， 当前可选择 {filteredData.length} 个项目
          </div>
        </div>
      )}
      <div className={styles.tableContainer || 'table-container'}>
        <Table
          dataSource={filteredData}
          columns={getColumns() as any}
          rowKey='id'
          rowSelection={rowSelection as any}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{
            y: 450,
            x: 'max-content',
          }}
          size='small'
          bordered
          rowClassName={(_, index) =>
            index % 2 === 0
              ? styles.tableRowLight || 'table-row-light'
              : styles.tableRowDark || 'table-row-dark'
          }
          style={{
            backgroundColor: '#fff',
          }}
          tableLayout='fixed'
        />
      </div>
    </Modal>
  );
};
