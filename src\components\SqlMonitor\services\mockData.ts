/**
 * 模拟数据服务
 * 提供开发和测试用的模拟数据
 */

import type { TaskAlert, DBConnection, AlertSend, OtherInfo } from '../types';

/**
 * 生成模拟告警数据
 */
export function generateMockAlerts(count: number = 50): TaskAlert[] {
  const alerts: TaskAlert[] = [];
  const severities = ['low', 'medium', 'high', 'critical'];
  const types = ['isExist', 'isEqual'];
  const tableNames = ['users', 'orders', 'products', 'logs', 'sessions', 'payments', 'inventory', 'customers', 'transactions', 'reports'];
  const systemNames = ['用户系统', '订单系统', '支付系统', '库存系统', '日志系统', '监控系统', '报表系统', '消息系统', '文件系统', '缓存系统'];

  for (let i = 1; i <= count; i++) {
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const tableName = tableNames[Math.floor(Math.random() * tableNames.length)];
    const systemName = systemNames[Math.floor(Math.random() * systemNames.length)];

    alerts.push({
      id: i,
      name: `${systemName}_告警_${i.toString().padStart(3, '0')}`,
      severity,
      sql: `SELECT COUNT(*) FROM ${tableName} WHERE status = 'error' AND created_at > NOW() - INTERVAL 1 HOUR`,
      type,
      values: type === 'isEqual' ? ['0', '1', '2'] : [],
      create_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      update_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return alerts;
}

/**
 * 生成模拟数据库连接数据
 */
export function generateMockDbConnections(count: number = 20): DBConnection[] {
  const connections: DBConnection[] = [];
  const dbTypes = ['mysql', 'oracle'];
  const hosts = ['*************', '*************', '*********', '*********', 'db-server-01', 'db-server-02'];
  const databases = ['user_db', 'order_db', 'product_db', 'log_db', 'analytics_db'];

  for (let i = 1; i <= count; i++) {
    const dbType = dbTypes[Math.floor(Math.random() * dbTypes.length)];
    const host = hosts[Math.floor(Math.random() * hosts.length)];
    const database = databases[Math.floor(Math.random() * databases.length)];

    connections.push({
      id: i,
      name: `${dbType.toUpperCase()}_连接_${i.toString().padStart(2, '0')}`,
      db_type: dbType,
      host,
      port: dbType === 'mysql' ? '3306' : '1521',
      user: `user_${i}`,
      passwd: btoa(`password_${i}`), // base64编码
      database: dbType === 'mysql' ? database : '',
      use_ssl: Math.random() > 0.5,
      server_timezone: 'Asia/Shanghai',
      instance: dbType === 'oracle' ? `ORCL${i}` : '',
      connect_method: dbType === 'oracle' ? (Math.random() > 0.5 ? 'sid' : 'service') : '',
      create_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      update_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return connections;
}

/**
 * 生成模拟告警发送数据
 */
export function generateMockAlertSends(count: number = 30): AlertSend[] {
  const sends: AlertSend[] = [];
  const types = ['kafka', 'prometheus'];
  const addresses = ['*************:9092', '*************:9092', '**********:9090', '**********:9090'];
  const topics = ['alerts', 'notifications', 'monitoring', 'system-events'];

  for (let i = 1; i <= count; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const address = addresses[Math.floor(Math.random() * addresses.length)];

    sends.push({
      id: i,
      name: `${type.toUpperCase()}_发送_${i.toString().padStart(2, '0')}`,
      type,
      kafka_receiver:
        type === 'kafka'
          ? {
              address,
              username: `kafka_user_${i}`,
              password: `kafka_pass_${i}`,
              topic: topics[Math.floor(Math.random() * topics.length)],
            }
          : {
              address: '',
              username: '',
              password: '',
              topic: '',
            },
      prometheus_receiver:
        type === 'prometheus'
          ? {
              address,
              username: `prom_user_${i}`,
              password: `prom_pass_${i}`,
            }
          : {
              address: '',
              username: '',
              password: '',
            },
      create_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      update_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return sends;
}

/**
 * 生成模拟其他信息数据
 */
export function generateMockOtherInfos(count: number = 25): OtherInfo[] {
  const infos: OtherInfo[] = [];
  const businessSystems = [
    { name: '用户管理系统', en: 'user-management' },
    { name: '订单管理系统', en: 'order-management' },
    { name: '商品管理系统', en: 'product-management' },
    { name: '库存管理系统', en: 'inventory-management' },
    { name: '支付系统', en: 'payment-system' },
    { name: '物流系统', en: 'logistics-system' },
    { name: '客服系统', en: 'customer-service' },
    { name: '数据分析系统', en: 'data-analytics' },
    { name: '监控系统', en: 'monitoring-system' },
    { name: '日志系统', en: 'logging-system' },
  ];

  const locations = [
    '北京机房',
    '上海机房',
    '广州机房',
    '深圳机房',
    '杭州机房',
    '成都机房',
    '武汉机房',
    '西安机房',
    '南京机房',
    '青岛机房',
    '阿里云华东1',
    '阿里云华北2',
    '腾讯云上海',
    '腾讯云北京',
    'AWS东京',
    'AWS新加坡',
    'Azure香港',
    'Azure首尔',
  ];

  const hostnames = [
    'web-server-01',
    'web-server-02',
    'api-server-01',
    'api-server-02',
    'db-server-01',
    'db-server-02',
    'cache-server-01',
    'cache-server-02',
    'mq-server-01',
    'mq-server-02',
    'monitor-server-01',
    'monitor-server-02',
  ];

  for (let i = 1; i <= count; i++) {
    const business = businessSystems[Math.floor(Math.random() * businessSystems.length)];
    const location = locations[Math.floor(Math.random() * locations.length)];
    const hostname = hostnames[Math.floor(Math.random() * hostnames.length)];

    infos.push({
      id: i,
      name: `${business.name}_信息_${i.toString().padStart(2, '0')}`,
      business: business.name,
      business_en: business.en,
      hostname,
      location,
      create_time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      update_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return infos;
}

// 导出预生成的模拟数据
export const mockAlertData = generateMockAlerts();
export const mockDbConnectionData = generateMockDbConnections();
export const mockAlertSendData = generateMockAlertSends();
export const mockOtherInfoData = generateMockOtherInfos();
