import React from 'react';
import { Layout, Button, Avatar, Dropdown, Space, Breadcrumb } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  RightOutlined,
} from '@ant-design/icons';
import type { HeaderProps } from '../types';
import { DEFAULT_USER } from '../constants';
import styles from '../DefaultLayout.module.css';

const { Header: AntHeader } = Layout;

/**
 * 头部导航栏组件
 */
export const Header: React.FC<HeaderProps> = ({
  collapsed,
  onToggleCollapse,
  breadcrumbItems,
  userMenuItems,
}) => {
  return (
    <AntHeader className={styles.header}>
      {/* 左侧：折叠按钮和面包屑 */}
      <Space size='middle'>
        <Button
          type='text'
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={onToggleCollapse}
          className={styles.triggerButton}
        />
        <Breadcrumb
          separator={<RightOutlined className={styles.breadcrumbSeparator} />}
          items={breadcrumbItems}
          className={styles.breadcrumb}
        />
      </Space>

      {/* 右侧：用户信息和通知 */}
      <Space size='middle'>
        {/* 通知铃铛 */}
        {/* <Badge count={5} size='small'>
          <Button type='text' icon={<BellOutlined />} className={styles.notificationButton} />
        </Badge> */}

        {/* 用户头像和下拉菜单 */}
        <Dropdown
          menu={{
            items: userMenuItems,
          }}
          placement='bottomRight'
          arrow
        >
          <Space className={styles.userDropdown}>
            <Avatar size={24} icon={<UserOutlined />} className={styles.userAvatar} />
            <span className={styles.userName}>{DEFAULT_USER.name}</span>
          </Space>
        </Dropdown>
      </Space>
    </AntHeader>
  );
};
