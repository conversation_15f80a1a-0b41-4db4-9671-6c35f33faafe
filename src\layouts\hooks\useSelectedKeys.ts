import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * 菜单选中状态 Hook
 */
export const useSelectedKeys = (): string[] => {
  const location = useLocation();

  const selectedKeys = useMemo(() => {
    const path = location.pathname;
    if (path === '/' || path === '/dashboard') return ['dashboard'];
    if (path === '/users') return ['users'];
    if (path === '/tasks') return ['tasks'];
    if (path === '/teams') return ['teams'];
    return ['dashboard'];
  }, [location.pathname]);

  return selectedKeys;
};
