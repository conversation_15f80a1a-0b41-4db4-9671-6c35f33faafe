import React from 'react';
import { Card, Button, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { TaskAlert } from '../../types';
import { formStyles } from '../../styles';

interface AlertConfigTabProps {
  alerts: TaskAlert[];
  onAlertsChange: (alerts: TaskAlert[]) => void;
  onAddAlert: () => void;
  onEditAlert: (index: number) => void;
  onSelectAlert: () => void;
}

/**
 * 告警配置标签页组件
 * 管理告警触发条件的配置
 */
const AlertConfigTab: React.FC<AlertConfigTabProps> = ({ alerts, onAlertsChange, onAddAlert, onEditAlert, onSelectAlert }) => {
  const handleDeleteAlert = (index: number) => {
    const newAlerts = alerts.filter((_, i) => i !== index);
    onAlertsChange(newAlerts);
  };

  const columns: ColumnsType<TaskAlert> = [
    {
      title: '告警名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '告警级别',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <span
          style={{
            color: severity === 'critical' ? '#ff4d4f' : severity === 'high' ? '#fa8c16' : severity === 'medium' ? '#faad14' : '#52c41a',
          }}
        >
          {severity}
        </span>
      ),
    },
    {
      title: '告警类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: 'SQL语句',
      dataIndex: 'sql',
      key: 'sql',
      ellipsis: true,
      render: (sql: string) => (
        <code
          style={{
            fontSize: '12px',
            background: '#f5f5f5',
            padding: '2px 4px',
          }}
        >
          {sql}
        </code>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: string, _record: TaskAlert, index: number) => (
        <Space size="small">
          <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEditAlert(index)}>
            编辑
          </Button>
          <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={() => handleDeleteAlert(index)}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="告警触发条件"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddAlert}>
              新增告警
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectAlert}>
              选择告警
            </Button>
          </Space>
        }
      >
        <Table dataSource={alerts} rowKey="id" size="small" pagination={false} columns={columns} />
        {alerts.length === 0 && <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无告警配置，请点击"新增告警"或"选择告警"添加</div>}
      </Card>
    </div>
  );
};

export default AlertConfigTab;
