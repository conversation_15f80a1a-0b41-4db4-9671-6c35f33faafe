import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Table, Input, Button, Space, App, Popconfirm, Form, Tag, Select } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 导入重构后的模块
import type {
  TaskBasicGroupFormData,
  TaskGroupSearchParams,
  TaskBasicGroupFormDataAdd,
  TaskBasicGroupFormDataUpdate,
} from '../../types';
import { TaskService } from '../../services';
import { httpClient } from '@/services/http/client';

// 分组表单组件
interface GroupFormModalProps {
  visible: boolean;
  editingData?: TaskBasicGroupFormData;
  onCancel: () => void;
  onSubmit: (data: TaskBasicGroupFormDataAdd | TaskBasicGroupFormDataUpdate) => void;
}

const GroupFormModal: React.FC<GroupFormModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue({
        id: editingData.id,
        name: editingData.name,
      });
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 根据是否为编辑模式处理字段
      let submitData: TaskBasicGroupFormDataAdd | TaskBasicGroupFormDataUpdate;

      if (editingData) {
        // 更新操作：需要id字段，不需要时间字段
        submitData = {
          id: editingData.id,
          name: values.name,
          is_used: editingData.is_used, // 保持原有的使用状态
        } as TaskBasicGroupFormDataUpdate;
      } else {
        // 新增操作：不需要id和时间字段
        submitData = {
          name: values.name,
          is_used: false, // 新增的分组默认未使用
        } as TaskBasicGroupFormDataAdd;
      }

      console.log('提交数据:', submitData);
      onSubmit(submitData);
      onCancel();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'errorFields' in error) {
        App.useApp().message.error('请检查表单输入');
      } else {
        App.useApp().message.error('表单验证失败: ' + String(error));
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingData ? '编辑分组' : '新增分组'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      maskClosable={false}
      width={500}
    >
      <Form form={form} layout='vertical' style={{ marginTop: 20 }}>
        <Form.Item
          label='分组名称'
          name='name'
          rules={[
            { required: true, message: '请输入分组名称' },
            { min: 2, max: 50, message: '分组名称长度应在2-50个字符之间' },
            {
              pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
              message: '分组名称只能包含中英文、数字、下划线和横线',
            },
          ]}
        >
          <Input placeholder='请输入分组名称' />
        </Form.Item>
      </Form>
    </Modal>
  );
};

interface GroupManagementModalProps {
  visible: boolean;
  onCancel: () => void;
}

/**
 * 分组管理Modal组件
 */
const GroupManagementModal: React.FC<GroupManagementModalProps> = ({ visible, onCancel }) => {
  const { message } = App.useApp();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TaskBasicGroupFormData[]>([]);
  const [searchText, setSearchText] = useState('');
  const [usedFilter, setUsedFilter] = useState<boolean | undefined>(undefined); // 使用状态筛选
  const [pagination, setPagination] = useState({
    current: 1,
    page_size: 20,
    total: 0,
  });

  // 表单相关状态
  const [formVisible, setFormVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState<TaskBasicGroupFormData | undefined>(undefined);

  // 多选相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TaskBasicGroupFormData[]>([]);

  // 加载数据 - 移除 pagination 依赖避免无限循环
  const loadData = useCallback(async (customParams?: Partial<TaskGroupSearchParams>) => {
    setLoading(true);

    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 500));

      const params = {
        // ...pagination,
        ...customParams,
      };

      console.log(params);

      const response = await TaskService.getTaskGroups(params);
      console.log(response);

      setData(response.data);
      // setTotal(response.total);

      // 使用传入的参数或默认值
      const current = customParams?.current || 1;
      const pageSize = customParams?.page_size || 20;

      setPagination({
        current,
        page_size: pageSize,
        total: response.total,
      });

      message.success('数据加载成功');
    } catch (error) {
      message.error('数据加载失败: ' + error);
    } finally {
      setLoading(false);
    }
  }, []); // 空依赖数组，避免无限循环

  // 搜索
  const handleSearch = useCallback(() => {
    // 搜索时清空选择状态
    handleClearSelection();
    loadData({
      name: searchText,
      is_used: usedFilter,
      current: 1,
      page_size: pagination.page_size,
    });
  }, [searchText, usedFilter, pagination.page_size, loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchText('');
    setUsedFilter(undefined);
    // 重置时清空选择状态
    handleClearSelection();
    loadData({
      current: 1,
      page_size: pagination.page_size,
    });
  }, [pagination.page_size, loadData]);

  // 编辑分组
  const handleEdit = (record: TaskBasicGroupFormData) => {
    // 检查分组是否正在使用中
    // if (record.is_used) {
    //   message.error(`分组 "${record.name}" 正在使用中，无法编辑`);
    //   return;
    // }

    setEditingGroup(record);
    setFormVisible(true);
  };

  // 删除分组
  const handleDelete = async (record: TaskBasicGroupFormData) => {
    // 检查分组是否正在使用中
    if (record.is_used) {
      message.error(`分组 "${record.name}" 正在使用中，无法删除`);
      return;
    }

    try {
      // 模拟删除API请求
      await new Promise(resolve => setTimeout(resolve, 300));
      // /api/v1/task/group/delete/id

      const params = {
        ids: [record.id],
      };
      console.log(params);

      const res = httpClient.post('/api/v1/task/group/delete/id', params);
      console.log('删除API返回数据：', res);

      message.success(`删除分组 "${record.name}" 成功`);
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        page_size: pagination.page_size,
      });
    } catch (error) {
      message.error('删除失败: ' + error);
    }
  };

  // 新增分组
  const handleAdd = () => {
    setEditingGroup(undefined);
    setFormVisible(true);
  };

  // 表单提交处理
  const handleFormSubmit = async (
    formData: TaskBasicGroupFormDataAdd | TaskBasicGroupFormDataUpdate
  ) => {
    try {
      if (editingGroup) {
        // 更新操作
        const updateData = formData as TaskBasicGroupFormDataUpdate;
        const res = await httpClient.post('/api/v1/task/group/update', updateData);
        console.log('更新分组响应:', res);
        message.success('编辑分组成功');
      } else {
        // 新增操作
        const addData = formData as TaskBasicGroupFormDataAdd;
        const res = await httpClient.post('/api/v1/task/group/add', addData);
        console.log('新增分组响应:', res);
        message.success('新增分组成功');
      }

      // 重新加载数据
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        page_size: pagination.page_size,
      });

      setFormVisible(false);
      setEditingGroup(undefined);
    } catch (error) {
      message.error('操作失败: ' + error);
    }
  };

  // 表单取消处理
  const handleFormCancel = () => {
    setFormVisible(false);
    setEditingGroup(undefined);
  };

  // 多选处理 - 支持跨页选择
  const handleSelectionChange = (newSelectedRowKeys: React.Key[]) => {
    // 获取当前页面的所有行键
    const currentPageKeys = data.map(item => item.id);

    // 保留非当前页面的已选择项（这些项目不在当前页面，所以不会被 Antd 的 onChange 影响）
    const preservedKeys = selectedRowKeys.filter(key => !currentPageKeys.includes(Number(key)));
    const preservedRows = selectedRows.filter(row => !currentPageKeys.includes(row.id));

    // 获取当前页面选中的行数据（基于 newSelectedRowKeys，这是 Antd 传递的当前页面的选择状态）
    const currentPageSelectedRows = data.filter(item => newSelectedRowKeys.includes(item.id));

    // 合并保留的选择和当前页面的新选择
    const finalSelectedKeys = [...preservedKeys, ...newSelectedRowKeys];
    const finalSelectedRows = [...preservedRows, ...currentPageSelectedRows];

    setSelectedRowKeys(finalSelectedKeys);
    setSelectedRows(finalSelectedRows);
  };

  // 取消选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的分组');
      return;
    }

    // 检查是否有正在使用中的分组
    const usedGroups = selectedRows.filter(row => row.is_used);
    if (usedGroups.length > 0) {
      const usedNames = usedGroups.map(group => group.name).join(', ');
      message.error(`以下分组正在使用中，无法删除: ${usedNames}`);
      return;
    }

    try {
      // 模拟批量删除API请求
      await new Promise(resolve => setTimeout(resolve, 800));

      const params = {
        ids: selectedRowKeys,
      };
      console.log(params);

      const res = httpClient.post('/api/v1/task/group/delete/id', params);
      console.log('删除API返回数据：', res);

      // 从模拟数据中删除选中的项
      const deletedNames = selectedRows.map(row => row.name);

      message.success(`成功删除 ${selectedRows.length} 个分组: ${deletedNames.join(', ')}`);

      // 清空选择
      handleClearSelection();

      // 重新加载数据
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: pagination.current,
        page_size: pagination.page_size,
      });
    } catch (error) {
      message.error('批量删除失败: ' + error);
    }
  };

  // 分页变化处理
  const handleTableChange = useCallback(
    (page: number, pageSize: number) => {
      loadData({
        name: searchText,
        is_used: usedFilter,
        current: page,
        page_size: pageSize,
      });
    },
    [searchText, usedFilter, loadData]
  );

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadData();
    } else {
      // 模态框关闭时清空选择状态
      handleClearSelection();
    }
  }, [visible, loadData]);

  // 表格列定义
  const columns: ColumnsType<TaskBasicGroupFormData> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '被使用',
      dataIndex: 'is_used',
      key: 'is_used',
      width: 80,
      sorter: (a, b) => Number(a.is_used) - Number(b.is_used),
      render: (is_used: boolean) => (
        <Tag color={is_used ? 'green' : 'default'}>{is_used ? '是' : '否'}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 200,
      ellipsis: true,
      sorter: (a, b) => a.create_time.localeCompare(b.create_time),
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 200,
      ellipsis: true,
      sorter: (a, b) => a.update_time.localeCompare(b.update_time),
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      render: (_, record) => (
        <Space size='small'>
          <Button
            type='link'
            size='small'
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            // disabled={record.is_used}
            title={record.is_used ? '该分组正在使用中，无法编辑' : '编辑分组'}
          >
            编辑
          </Button>
          <Popconfirm
            title='确认删除'
            description={
              record.is_used ? '该分组正在使用中，无法删除' : `确定要删除分组 "${record.name}" 吗？`
            }
            onConfirm={() => handleDelete(record)}
            okText='确认'
            cancelText='取消'
            disabled={record.is_used}
          >
            <Button
              type='link'
              size='small'
              danger
              icon={<DeleteOutlined />}
              disabled={record.is_used}
              title={record.is_used ? '该分组正在使用中，无法删除' : '删除分组'}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <TeamOutlined className='text-blue-500' />
          <span>分组管理</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      maskClosable={false}
      styles={{
        body: { padding: '20px 24px' },
      }}
    >
      {/* 搜索区域 */}
      <div className='mb-6 p-4 bg-gray-50 rounded-lg'>
        <div className='flex justify-between items-center'>
          <div className='flex items-center gap-3'>
            <Input
              placeholder='请输入分组名称'
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 280 }}
              allowClear
              className='shadow-sm'
              autoComplete='off'
              autoCorrect='off'
              autoCapitalize='off'
              spellCheck={false}
            />
            <Select
              placeholder='选择使用状态'
              value={usedFilter}
              onChange={setUsedFilter}
              style={{ width: 150 }}
              allowClear
              className='shadow-sm'
              options={[
                // { label: '全部', value: undefined },
                { label: '已使用', value: true },
                { label: '未使用', value: false },
              ]}
            />
          </div>
          <div>
            <Space>
              <Button
                type='primary'
                icon={<SearchOutlined />}
                onClick={handleSearch}
                className='shadow-sm'
              >
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset} className='shadow-sm'>
                重置
              </Button>
              <Button
                type='primary'
                icon={<PlusOutlined />}
                onClick={handleAdd}
                className='shadow-sm'
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  border: 'none',
                }}
              >
                新增分组
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 选择提示栏 */}
      {selectedRowKeys.length > 0 && (
        <div className='mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex justify-between items-center'>
          <div className='text-blue-700'>
            已选择 <span className='font-semibold'>{selectedRowKeys.length}</span> 项
          </div>
          <div>
            <Space>
              <Popconfirm
                title='确认批量删除'
                description={`确定要删除选中的 ${selectedRowKeys.length} 个分组吗？`}
                onConfirm={handleBatchDelete}
                okText='确认'
                cancelText='取消'
              >
                <Button type='primary' danger icon={<DeleteOutlined />} size='small'>
                  批量删除
                </Button>
              </Popconfirm>
              <Button size='small' onClick={handleClearSelection}>
                取消选择
              </Button>
            </Space>
          </div>
        </div>
      )}

      {/* 表格 */}
      <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
        <Table
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey='id'
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
            // 使用自定义的跨页选择逻辑，不使用 Antd 内置的
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.page_size,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
            pageSizeOptions: ['20', '30', '50', '100'],
          }}
          size='small'
          className='rounded-lg overflow-hidden  px-2'
          scroll={{ y: 400 }}
        />
      </div>

      {/* 分组表单弹窗 */}
      <GroupFormModal
        visible={formVisible}
        editingData={editingGroup}
        onCancel={handleFormCancel}
        onSubmit={handleFormSubmit}
      />
    </Modal>
  );
};

export default GroupManagementModal;
