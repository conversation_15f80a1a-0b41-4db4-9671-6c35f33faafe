import React from 'react';
import { But<PERSON>, Space } from 'antd';
import type { SelectionState } from '../../types/user';

/**
 * 选择提示组件Props
 */
interface SelectionAlertProps {
  selection: SelectionState;
  onClearSelection: () => void;
  onBatchDelete?: () => void;
  showBatchDelete?: boolean;
}

/**
 * 选择提示组件
 * 显示当前选中的项目数量和相关操作按钮
 */
export const SelectionAlert: React.FC<SelectionAlertProps> = ({
  selection,
  onClearSelection,
  onBatchDelete,
  showBatchDelete = true,
}) => {
  const { selectedRowKeys } = selection;

  // 如果没有选中项，不显示组件
  if (selectedRowKeys.length === 0) {
    return null;
  }

  return (
    <div
      style={{
        padding: '12px 16px',
        backgroundColor: '#e6f7ff',
        border: '1px solid #91d5ff',
        borderRadius: '6px',
        marginBottom: '16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <span
          style={{
            color: '#1890ff',
            fontWeight: 500,
          }}
        >
          已选择 {selectedRowKeys.length} 项
        </span>
        <Button
          type='link'
          size='small'
          onClick={onClearSelection}
          style={{
            padding: '0 8px',
            height: 'auto',
          }}
        >
          清除选择
        </Button>
      </div>

      {showBatchDelete && onBatchDelete && (
        <Space>
          <Button
            type='primary'
            danger
            size='small'
            onClick={onBatchDelete}
            disabled={selectedRowKeys.length === 0}
          >
            批量删除
          </Button>
        </Space>
      )}
    </div>
  );
};
