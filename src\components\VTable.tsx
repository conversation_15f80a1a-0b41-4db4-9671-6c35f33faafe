import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { ListTable } from '@visactor/vtable';
import { Button, Space, Drawer, Form, Input, Select, Modal, App } from 'antd';

// interface taskData {
//   id: number;
//   // 任务名称
//   name: string;
//   // 任务分组
//   group: string;
//   // 开始时间, 格式为HH:mm:ss
//   start_time: string;
//   // 结束时间, 格式为HH:mm:ss
//   end_time: string;
//   // 星期，格式 1-7 / 1,2,3,4,5
//   weekday: string;
//   // 执行频率
//   frequency: string;
//   // 重试次数
//   retryNum: string;
//   // 重试间隔
//   retry_frequency: string;
//   // 告警触发条件
//   alert_trigger: string;

//   alert_receiver: string;
// }

// 扩展 HTMLDivElement 接口以包含自定义属性
interface ExtendedHTMLDivElement extends HTMLDivElement {
  _handleRightClick?: (event: MouseEvent) => void;
}

interface TableData {
  id: number;
  name: string;
  age: number;
  email: string;
  department: string;
  selected?: boolean;
  cellSelected?: boolean; // 添加单元格选中状态
}

interface ContainerSize {
  width: number;
  height: number;
}

// 生成模拟数据的常量 - 移到组件外部避免依赖问题
const DEPARTMENTS = [
  '技术部',
  '市场部',
  '人事部',
  '财务部',
  '运营部',
  '设计部',
  '产品部',
  '销售部',
];

// 动画配置常量 - 移到组件外部避免重新创建
const ANIMATION_CONFIG = {
  duration: 300,
  delay: 100,
  type: 'one-by-one' as const, // all
  direction: 'row' as const, // column
};

const FIRST_NAMES = [
  '张',
  '李',
  '王',
  '刘',
  '陈',
  '杨',
  '赵',
  '黄',
  '周',
  '吴',
  '徐',
  '孙',
  '胡',
  '朱',
  '高',
  '林',
];

const SECOND_NAMES = [
  '伟',
  '芳',
  '娜',
  '秀英',
  '敏',
  '静',
  '丽',
  '强',
  '磊',
  '军',
  '洋',
  '勇',
  '艳',
  '杰',
  '涛',
  '明',
  '超',
  '秀兰',
];

const VTable: React.FC = () => {
  const { message } = App.useApp();
  // 状态管理
  const [tableData, setTableData] = useState<TableData[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [containerSize, setContainerSize] = useState<ContainerSize>({
    width: 0,
    height: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  // 编辑抽屉相关状态
  const [editDrawerVisible, setEditDrawerVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TableData | null>(null);
  const [form] = Form.useForm();
  const [exitConfirmVisible, setExitConfirmVisible] = useState(false);

  // 右键菜单相关状态
  const [contextMenuVisible, setContextMenuVisible] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({
    x: 0,
    y: 0,
  });
  const [contextMenuRecord, setContextMenuRecord] = useState<TableData | null>(null);

  // 删除确认Modal相关状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteRecord, setDeleteRecord] = useState<TableData | null>(null);

  // 引用管理
  const tableRef = useRef<ListTable | null>(null);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 表格状态管理
  const isTableInitializedRef = useRef<boolean>(false);
  const lastTableDataRef = useRef<TableData[]>([]);
  const tableInstanceIdRef = useRef<number>(0); // 表格实例ID，用于跟踪重建
  const pendingDataUpdateRef = useRef<TableData[] | null>(null); // 待更新的数据

  // 动画和加载状态
  const isFirstLoadRef = useRef<boolean>(true);

  // 双击检测相关
  const lastClickTimeRef = useRef<number>(0);
  const lastClickPositionRef = useRef<{
    row: number;
    col: number;
  } | null>(null);

  // 生成模拟数据
  const generateMockData = useCallback((): TableData[] => {
    try {
      setIsLoading(true);
      console.log('开始生成10000条数据...');
      const startTime = performance.now();

      const data = Array.from({ length: 10000 }, (_, index) => ({
        id: index + 1,
        name: `${FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)]}${SECOND_NAMES[Math.floor(Math.random() * SECOND_NAMES.length)]}`,
        age: 18 + Math.floor(Math.random() * 47),
        email: `user${(index + 1).toString().padStart(5, '0')}@company.com`,
        department: DEPARTMENTS[Math.floor(Math.random() * DEPARTMENTS.length)],
        selected: false,
      }));

      const endTime = performance.now();
      console.log(`数据生成完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

      return data;
    } catch (error) {
      console.error('生成数据时发生错误:', error);
      message.error('生成数据失败，请重试');
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 初始化数据 - 默认为空，让用户主动生成数据
  useEffect(() => {
    // 组件初始化时不自动生成数据，保持空状态
    setTableData([]);
  }, []);

  // 同步 tableData 到 ref，用于事件处理
  useEffect(() => {
    lastTableDataRef.current = tableData;
  }, [tableData]);

  // 自适应容器尺寸
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerSize({
          width: clientWidth,
          height: clientHeight,
        });
      }
    };

    // 初始化尺寸
    updateSize();

    // 监听窗口大小变化
    window.addEventListener('resize', updateSize);

    return () => {
      window.removeEventListener('resize', updateSize);
    };
  }, []);

  // 计算选择状态的辅助函数
  const getSelectionState = useMemo(() => {
    const totalCount = tableData.length;
    const selectedCount = selectedRowKeys.length;

    return {
      isAllSelected: totalCount > 0 && selectedCount === totalCount,
      isIndeterminate: selectedCount > 0 && selectedCount < totalCount,
      selectedCount,
      totalCount,
    };
  }, [tableData.length, selectedRowKeys.length]);

  // 数据重置处理 - 用于重新生成数据等操作
  const resetTableData = useCallback((newData: TableData[]) => {
    console.log(`数据重置请求，新数据条数: ${newData.length}`);

    // 清除选择状态
    setSelectedRowKeys([]);

    // 标记表格需要重建
    isTableInitializedRef.current = false;
    pendingDataUpdateRef.current = newData;

    // 更新数据状态
    setTableData(newData);

    console.log('数据重置完成，表格将在下次渲染时重建');
  }, []);

  // 优化的行选择处理 - 智能更新，避免冲突
  const updateRowSelection = useCallback((rowId: number, checked: boolean) => {
    console.log(`行选择更新请求: ${rowId} -> ${checked}`);

    setTableData(prevData => {
      const updatedData = prevData.map(item =>
        item.id === rowId
          ? {
              ...item,
              selected: checked,
            }
          : item
      );

      // 立即更新表格显示，避免等待状态同步
      if (tableRef.current && isTableInitializedRef.current) {
        try {
          tableRef.current.setRecords(updatedData);
          // 同步更新引用数据
          lastTableDataRef.current = [...updatedData];
          console.log(`行选择已同步到表格: ${rowId} -> ${checked}`);
        } catch (error) {
          console.error('同步行选择到表格失败:', error);
        }
      }

      return updatedData;
    });

    setSelectedRowKeys(prevKeys => {
      if (checked) {
        return prevKeys.includes(rowId) ? prevKeys : [...prevKeys, rowId];
      } else {
        return prevKeys.filter(key => key !== rowId);
      }
    });
  }, []);

  // 全选/取消全选 - 智能更新
  const handleSelectAll = useCallback((checked: boolean) => {
    try {
      console.log(`全选操作请求: ${checked}`);

      setTableData(prevData => {
        const updatedData = prevData.map(item => ({
          ...item,
          selected: checked,
        }));

        setSelectedRowKeys(checked ? updatedData.map(item => item.id) : []);

        // 立即更新表格显示
        if (tableRef.current && isTableInitializedRef.current) {
          try {
            tableRef.current.setRecords(updatedData);
            // 同步更新引用数据
            lastTableDataRef.current = [...updatedData];
            console.log(`全选操作已同步到表格: ${checked}`);
          } catch (error) {
            console.error('同步全选操作到表格失败:', error);
          }
        }

        return updatedData;
      });
    } catch (error) {
      console.error('全选操作时发生错误:', error);
      message.error('操作失败，请重试');
    }
  }, []);

  // 清除选择
  const clearSelection = useCallback(() => {
    try {
      setTableData(prevData =>
        prevData.map(item => ({
          ...item,
          selected: false,
        }))
      );
      setSelectedRowKeys([]);
      message.info('已清除所有选择');
    } catch (error) {
      console.error('清除选择时发生错误:', error);
      message.error('操作失败，请重试');
    }
  }, []);

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    const selectedCount = selectedRowKeys.length;
    if (selectedCount === 0) {
      message.warning('请先选择要删除的行');
      return;
    }

    try {
      setTableData(prevData => prevData.filter(item => !selectedRowKeys.includes(item.id)));
      setSelectedRowKeys([]);
      message.success(`已删除 ${selectedCount} 条记录`);
    } catch (error) {
      console.error('批量删除时发生错误:', error);
      message.error('删除失败，请重试');
    }
  }, [selectedRowKeys]);

  // 清空所有数据
  const handleClearAllData = useCallback(() => {
    try {
      console.log('清空所有数据');
      resetTableData([]);
      message.success('已清空所有数据');
    } catch (error) {
      console.error('清空数据时发生错误:', error);
      message.error('清空失败，请重试');
    }
  }, [resetTableData]);

  // 编辑记录
  const handleEdit = useCallback(
    (record: TableData) => {
      try {
        console.log('开始编辑记录:', record);
        setEditingRecord(record);
        form.setFieldsValue({
          name: record.name,
          age: record.age,
          email: record.email,
          department: record.department,
        });
        setEditDrawerVisible(true);
        message.info(`正在编辑：${record.name}`);
      } catch (error) {
        console.error('打开编辑抽屉时发生错误:', error);
        message.error('打开编辑失败，请重试');
      }
    },
    [form]
  );

  // 保存编辑
  const handleSaveEdit = useCallback(async () => {
    try {
      const values = await form.validateFields();
      if (!editingRecord) return;

      console.log('保存编辑:', editingRecord.name, '新值:', values);

      // 确保年龄字段是数字类型
      const processedValues = {
        ...values,
        age: typeof values.age === 'string' ? parseInt(values.age, 10) : values.age,
      };

      setTableData(prevData =>
        prevData.map(item =>
          item.id === editingRecord.id
            ? {
                ...item,
                ...processedValues,
              }
            : item
        )
      );

      setEditDrawerVisible(false);
      setEditingRecord(null);
      form.resetFields();
      message.success(`📝 ${editingRecord.name} 编辑成功`);
    } catch (error) {
      console.error('保存编辑时发生错误:', error);
      message.error('保存失败，请重试');
    }
  }, [form, editingRecord]);

  // 检查表单是否有变化
  const hasFormChanges = useCallback(() => {
    if (!editingRecord) return false;

    const currentValues = form.getFieldsValue();
    // 确保年龄字段的类型一致性
    const currentAge =
      typeof currentValues.age === 'string' ? parseInt(currentValues.age, 10) : currentValues.age;

    return (
      currentValues.name !== editingRecord.name ||
      currentAge !== editingRecord.age ||
      currentValues.email !== editingRecord.email ||
      currentValues.department !== editingRecord.department
    );
  }, [form, editingRecord]);

  // 处理抽屉关闭请求
  const handleDrawerClose = useCallback(() => {
    if (hasFormChanges()) {
      setExitConfirmVisible(true);
    } else {
      setEditDrawerVisible(false);
      setEditingRecord(null);
      form.resetFields();
    }
  }, [hasFormChanges, form]);

  // 取消编辑
  const handleCancelEdit = useCallback(() => {
    if (hasFormChanges()) {
      setExitConfirmVisible(true);
    } else {
      setEditDrawerVisible(false);
      setEditingRecord(null);
      form.resetFields();
    }
  }, [hasFormChanges, form]);

  // 确认退出编辑
  const handleConfirmExit = useCallback(() => {
    setExitConfirmVisible(false);
    setEditDrawerVisible(false);
    setEditingRecord(null);
    form.resetFields();
  }, [form]);

  // 取消退出编辑
  const handleCancelExit = useCallback(() => {
    setExitConfirmVisible(false);
  }, []);

  // 显示删除确认Modal
  const showDeleteModal = useCallback((record: TableData) => {
    setDeleteRecord(record);
    setDeleteModalVisible(true);
    // 关闭右键菜单
    setContextMenuVisible(false);
    setContextMenuRecord(null);
  }, []);

  // 确认删除
  const handleConfirmDelete = useCallback(() => {
    if (!deleteRecord) return;

    try {
      console.log('删除记录:', deleteRecord);
      setTableData(prevData => prevData.filter(item => item.id !== deleteRecord.id));

      // 如果删除的记录在选中列表中，也要从选中列表中移除
      setSelectedRowKeys(prevKeys => prevKeys.filter(key => key !== deleteRecord.id));

      // 关闭Modal
      setDeleteModalVisible(false);
      setDeleteRecord(null);

      message.success(`🗑️ 已删除记录：${deleteRecord.name}`);
    } catch (error) {
      console.error('删除记录时发生错误:', error);
      message.error('删除失败，请重试');
    }
  }, [deleteRecord]);

  // 取消删除
  const handleCancelDelete = useCallback(() => {
    setDeleteModalVisible(false);
    setDeleteRecord(null);
  }, []);

  // 处理右键菜单
  const handleContextMenu = useCallback((record: TableData, event: MouseEvent) => {
    event.preventDefault();
    setContextMenuRecord(record);
    setContextMenuPosition({
      x: event.clientX,
      y: event.clientY,
    });
    setContextMenuVisible(true);
  }, []);

  // 关闭右键菜单
  const handleCloseContextMenu = useCallback(() => {
    setContextMenuVisible(false);
    setContextMenuRecord(null);
  }, []);

  // 右键菜单编辑
  const handleContextMenuEdit = useCallback(() => {
    if (contextMenuRecord) {
      handleEdit(contextMenuRecord);
      handleCloseContextMenu();
    }
  }, [contextMenuRecord, handleEdit, handleCloseContextMenu]);

  // 右键菜单删除
  const handleContextMenuDelete = useCallback(() => {
    if (contextMenuRecord) {
      showDeleteModal(contextMenuRecord);
    }
  }, [contextMenuRecord, showDeleteModal]);

  // 右键菜单复制
  const handleContextMenuCopy = useCallback(async () => {
    if (contextMenuRecord) {
      try {
        const copyText = `姓名: ${contextMenuRecord.name}\n年龄: ${contextMenuRecord.age}\n邮箱: ${contextMenuRecord.email}\n部门: ${contextMenuRecord.department}`;

        if (navigator.clipboard && window.isSecureContext) {
          // 使用现代 Clipboard API
          await navigator.clipboard.writeText(copyText);
        } else {
          // 降级方案：使用传统方法
          const textArea = document.createElement('textarea');
          textArea.value = copyText;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          document.execCommand('copy');
          textArea.remove();
        }

        message.success(`📋 已复制 ${contextMenuRecord.name} 的信息到剪贴板`);
        handleCloseContextMenu();
      } catch (error) {
        console.error('复制失败:', error);
        message.error('复制失败，请重试');
      }
    }
  }, [contextMenuRecord, handleCloseContextMenu]);

  // 确保selectedRowKeys与实际存在的数据保持同步
  useEffect(() => {
    try {
      const existingIds = tableData.map(item => item.id);
      const validSelectedKeys = selectedRowKeys.filter(key => existingIds.includes(key));

      // 如果有无效的选中项，清理它们
      if (validSelectedKeys.length !== selectedRowKeys.length) {
        setSelectedRowKeys(validSelectedKeys);
      }
    } catch (error) {
      console.error('同步选中状态时发生错误:', error);
    }
  }, [tableData, selectedRowKeys]);

  // 添加全局点击事件监听器来关闭右键菜单
  useEffect(() => {
    const handleGlobalClick = () => {
      if (contextMenuVisible) {
        setContextMenuVisible(false);
        setContextMenuRecord(null);
      }
    };

    if (contextMenuVisible) {
      document.addEventListener('click', handleGlobalClick);
    }

    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, [contextMenuVisible]);

  // 禁用整个页面的默认右键菜单
  useEffect(() => {
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
    };

    document.addEventListener('contextmenu', handleContextMenu);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  // 列配置 - 使用VTable原生checkbox功能
  const columns = useMemo(
    () => [
      {
        field: 'selected',
        cellType: 'checkbox' as const,
        headerType: 'checkbox' as const,
        width: '4%',
        minWidth: 40,
        headerStyle: {
          textAlign: 'center' as const,
          fontWeight: 600,
          color: '#374151',
          indeterminate: getSelectionState.isIndeterminate,
        },
        style: {
          textAlign: 'center' as const,
          fontWeight: 500,
          color: '#6b7280',
        },
      },
      {
        field: 'id',
        title: 'ID',
        width: '10%',
        minWidth: 80,
        sort: true,
        headerStyle: {
          textAlign: 'center' as const,
          fontWeight: 600,
          color: '#374151',
        },
        style: {
          textAlign: 'center' as const,
          fontWeight: 500,
          color: '#6b7280',
        },
      },
      {
        field: 'name',
        title: '姓名',
        width: '15%',
        minWidth: 100,
        sort: true,
        headerStyle: {
          textAlign: 'left' as const,
          fontWeight: 600,
          color: '#374151',
        },
        style: {
          textAlign: 'left' as const,
          fontWeight: 500,
          color: '#111827',
        },
      },
      {
        field: 'age',
        title: '年龄',
        width: '10%',
        minWidth: 80,
        sort: true,
        headerStyle: {
          textAlign: 'center' as const,
          fontWeight: 600,
          color: '#374151',
        },
        style: {
          textAlign: 'center' as const,
          fontWeight: 500,
          color: '#6b7280',
        },
      },
      {
        field: 'email',
        title: '邮箱',
        width: '35%',
        minWidth: 200,
        sort: true,
        headerStyle: {
          textAlign: 'left' as const,
          fontWeight: 600,
          color: '#374151',
        },
        style: {
          textAlign: 'left' as const,
          fontWeight: 400,
          color: '#4f46e5',
        },
      },
      {
        field: 'department',
        title: '部门',
        width: '34%',
        minWidth: 100,
        sort: true,
        headerStyle: {
          textAlign: 'left' as const,
          fontWeight: 600,
          color: '#374151',
        },
        style: {
          textAlign: 'left' as const,
          fontWeight: 400,
          color: '#374151',
        },
      },
    ],
    [getSelectionState.isIndeterminate]
  );

  // 监听选中状态变化，输出调试信息
  useEffect(() => {
    console.log('状态更新:', {
      totalCount: getSelectionState.totalCount,
      selectedCount: getSelectionState.selectedCount,
      indeterminate: getSelectionState.isIndeterminate,
      checked: getSelectionState.isAllSelected,
    });
  }, [getSelectionState]);

  // 稳定的表格基础配置 - 不包含动画配置，避免重建
  const baseTableConfig = useMemo(
    () => ({
      widthMode: 'adaptive' as const,
      autoFillWidth: true,
      defaultRowHeight: 40,
      defaultHeaderRowHeight: 45,
      limitMinWidth: 30,
    }),
    [] // 空依赖数组，配置只创建一次
  );

  // 动态主题配置 - 依赖 tableData 和 selectedCellPosition 来处理选择状态样式
  const themeConfig = useMemo(
    () => ({
      headerStyle: {
        bgColor: '#f8fafc',
        color: '#374151',
        fontSize: 14,
        fontWeight: 600,
        textAlign: 'center' as const,
        borderColor: '#e5e7eb',
        borderLineWidth: 1,
      },
      bodyStyle: {
        bgColor: (args: { row: number; col: number }) => {
          try {
            const dataRowIndex = args.row - 1;
            if (dataRowIndex < 0) return '#ffffff';

            const record = tableData[dataRowIndex];

            // 如果该行被选中（多选状态）
            const isRowSelected = record?.selected;

            if (isRowSelected) {
              // 多选状态：整行使用多选颜色
              return args.col === 0 ? '#dbeafe' : '#eff6ff';
            }

            return dataRowIndex % 2 === 0 ? '#ffffff' : '#f9fafb';
          } catch (error) {
            console.error('计算背景色时发生错误:', error);
            return '#ffffff';
          }
        },
        color: (args: { row: number; col: number }) => {
          try {
            const dataRowIndex = args.row - 1;
            if (dataRowIndex < 0) return '#374151';

            const record = tableData[dataRowIndex];
            const isRowSelected = record?.selected;

            if (isRowSelected) {
              return '#1e40af'; // 多选状态文字颜色
            }

            return '#374151'; // 默认文字颜色
          } catch (error) {
            console.error('计算文字颜色时发生错误:', error);
            return '#374151';
          }
        },
        fontSize: 13,
        textAlign: 'left' as const,
        borderColor: '#f3f4f6',
        borderLineWidth: 1,
      },
      frameStyle: {
        borderColor: '#e5e7eb',
        borderLineWidth: 1,
        cornerRadius: 8,
        shadowBlur: 4,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
    }),
    [tableData] // 只依赖 tableData，移除单元格选中状态依赖
  );

  // 智能表格数据同步 - 区分数据重置和选择状态变化
  useEffect(() => {
    if (!tableRef.current || tableData.length === 0) {
      return;
    }

    try {
      const lastData = lastTableDataRef.current;

      // 检查数据变化类型
      const isDataReset =
        lastData.length !== tableData.length ||
        !lastData.every((item, index) => {
          const currentItem = tableData[index];
          return currentItem && item.id === currentItem.id;
        });

      const isOnlySelectionChange =
        !isDataReset &&
        lastData.length === tableData.length &&
        lastData.every((item, index) => {
          const currentItem = tableData[index];
          return (
            item.id === currentItem.id &&
            item.name === currentItem.name &&
            item.age === currentItem.age &&
            item.email === currentItem.email &&
            item.department === currentItem.department
          );
        });

      if (isDataReset) {
        // 数据重置：标记需要重建表格
        console.log('检测到数据重置，标记表格需要重建');
        isTableInitializedRef.current = false;
        pendingDataUpdateRef.current = tableData;
      } else if (isOnlySelectionChange) {
        // 只是选择状态变化：直接更新表格数据
        tableRef.current.setRecords(tableData);
        console.log('只更新选择状态，保持滚动位置');
        lastTableDataRef.current = [...tableData];
      }
    } catch (error) {
      console.error('同步表格数据时发生错误:', error);
    }
  }, [tableData]);

  // 智能表格创建和重建逻辑
  useEffect(() => {
    const containerElement = tableContainerRef.current;

    if (!containerElement || !containerSize.width || !containerSize.height) {
      return;
    }

    // 如果没有数据，销毁现有实例
    if (tableData.length === 0) {
      if (tableRef.current) {
        try {
          tableRef.current.release();
          console.log('数据为空，销毁表格实例');
        } catch (error) {
          console.error('销毁表格实例时发生错误:', error);
        }
        tableRef.current = null;
        isTableInitializedRef.current = false;
        lastTableDataRef.current = [];
      }
      return;
    }

    // 检查是否需要重建表格
    const needsRebuild =
      !tableRef.current || !isTableInitializedRef.current || pendingDataUpdateRef.current !== null;

    if (!needsRebuild) {
      console.log('表格已存在且无需重建，跳过创建');
      return;
    }

    // 确定要使用的数据
    const dataToUse = pendingDataUpdateRef.current || tableData;
    console.log('准备创建/重建表格，数据条数:', dataToUse.length);

    try {
      // 销毁现有实例（如果存在）
      if (tableRef.current) {
        try {
          tableRef.current.release();
          console.log('销毁旧的表格实例');
        } catch (error) {
          console.error('销毁旧表格实例时发生错误:', error);
        }
      }

      // 创建新的表格实例
      console.log('创建表格，列配置:', columns);
      console.log('数据示例:', dataToUse.slice(0, 2));

      const table = new ListTable({
        container: containerElement,
        records: dataToUse,
        columns: columns,
        ...baseTableConfig,
        animationAppear: isFirstLoadRef.current ? ANIMATION_CONFIG : false,
        theme: themeConfig,
      });

      // 绑定事件
      table.on('checkbox_state_change', (args: { row: number; col: number; checked: boolean }) => {
        try {
          console.log('Checkbox状态变化事件:', args);
          const { row, checked } = args;
          const dataRowIndex = row - 1;

          if (dataRowIndex >= 0) {
            // 获取当前行数据
            const record = lastTableDataRef.current[dataRowIndex];
            if (record) {
              console.log('选择的记录:', record.name, '新状态:', checked);
              // 使用优化的选择更新函数
              updateRowSelection(record.id, checked);
            }
          } else if (row === 0) {
            const shouldSelectAll = !getSelectionState.isAllSelected;

            console.log(
              '表头checkbox点击 - 全选状态:',
              getSelectionState.isAllSelected,
              '执行操作:',
              shouldSelectAll ? '全选' : '取消全选'
            );

            handleSelectAll(shouldSelectAll);
          }
        } catch (error) {
          console.error('处理checkbox状态变化时发生错误:', error);
        }
      });

      table.on('click_cell', (args: { row: number; col: number }) => {
        try {
          console.log('单元格点击:', args);

          // 双击检测逻辑
          const currentTime = Date.now();
          const lastClickTime = lastClickTimeRef.current;
          const lastClickPosition = lastClickPositionRef.current;

          const isDoubleClick =
            currentTime - lastClickTime < 300 && // 300ms内
            lastClickPosition &&
            lastClickPosition.row === args.row &&
            lastClickPosition.col === args.col;

          if (isDoubleClick) {
            // 处理双击事件
            const dataRowIndex = args.row - 1;
            // 使用当前最新的 tableData 状态
            setTableData(currentData => {
              if (dataRowIndex >= 0 && dataRowIndex < currentData.length) {
                const record = currentData[dataRowIndex];
                if (record) {
                  console.log('双击编辑记录:', record.name);
                  handleEdit(record);
                }
              }
              return currentData; // 不修改数据，只是为了获取最新状态
            });
            return; // 双击时不执行单击逻辑
          }

          // 更新点击时间和位置
          lastClickTimeRef.current = currentTime;
          lastClickPositionRef.current = {
            row: args.row,
            col: args.col,
          };

          if (args.col === 0) {
            const dataRowIndex = args.row - 1;

            if (dataRowIndex >= 0) {
              // 获取当前行数据
              const record = lastTableDataRef.current[dataRowIndex];
              if (record) {
                const newChecked = !record.selected;
                console.log('点击checkbox单元格，切换状态:', record.name, '→', newChecked);
                // 使用优化的选择更新函数
                updateRowSelection(record.id, newChecked);
              }
            } else if (args.row === 0) {
              const shouldSelectAll = !getSelectionState.isAllSelected;

              console.log(
                '表头checkbox点击(单元格) - 全选状态:',
                getSelectionState.isAllSelected,
                '执行操作:',
                shouldSelectAll ? '全选' : '取消全选'
              );

              handleSelectAll(shouldSelectAll);
            }
          }
        } catch (error) {
          console.error('处理单元格点击时发生错误:', error);
        }
      });

      tableRef.current = table;
      isTableInitializedRef.current = true;
      tableInstanceIdRef.current += 1;

      // 更新数据引用和清理待更新数据
      lastTableDataRef.current = [...dataToUse];
      pendingDataUpdateRef.current = null;

      console.log(
        `表格创建完成 (实例ID: ${tableInstanceIdRef.current})，数据条数: ${dataToUse.length}`
      );

      // 添加右键事件监听 - 使用原生DOM事件
      let rightClickRecord: TableData | null = null;

      // 监听鼠标移动来跟踪当前悬停的记录
      table.on('mouseenter_cell', (args: { row: number; col: number }) => {
        const dataRowIndex = args.row - 1;
        // 使用当前最新的 tableData 状态
        setTableData(currentData => {
          if (dataRowIndex >= 0 && dataRowIndex < currentData.length) {
            rightClickRecord = currentData[dataRowIndex];
          }
          return currentData; // 不修改数据，只是为了获取最新状态
        });
      });

      // 绑定右键事件到表格容器
      const handleRightClick = (event: MouseEvent) => {
        event.preventDefault();
        if (rightClickRecord) {
          handleContextMenu(rightClickRecord, event);
        }
      };

      if (containerElement) {
        containerElement.addEventListener('contextmenu', handleRightClick);

        // 保存事件处理函数的引用，用于清理
        (containerElement as ExtendedHTMLDivElement)._handleRightClick = handleRightClick;
      }

      // 标记首次加载完成，后续不再显示动画
      if (isFirstLoadRef.current) {
        isFirstLoadRef.current = false;
        console.log('首次加载完成，后续操作将不显示动画');
      }
    } catch (error) {
      console.error('初始化表格时发生错误:', error);
      message.error('表格初始化失败，请刷新页面重试');
    }

    // 清理函数
    return () => {
      // 清理原生事件监听器
      if (containerElement) {
        const rightClickHandler = (containerElement as ExtendedHTMLDivElement)._handleRightClick;

        if (rightClickHandler) {
          containerElement.removeEventListener('contextmenu', rightClickHandler);
        }

        // 清理引用
        delete (containerElement as ExtendedHTMLDivElement)._handleRightClick;
      }

      // 清理表格实例
      if (tableRef.current) {
        try {
          tableRef.current.release();
        } catch (error) {
          console.error('清理表格实例时发生错误:', error);
        }
        tableRef.current = null;
      }
    };
  }, [
    // 包含必要的依赖项 - 但选择状态变化会被上面的 useEffect 拦截
    tableData,
    containerSize.width,
    containerSize.height,
    columns,
    baseTableConfig,
    themeConfig,
    updateRowSelection,
    handleSelectAll,
    handleEdit,
    handleContextMenu,
    getSelectionState.isAllSelected,
  ]);

  return (
    <div
      ref={containerRef}
      style={{
        height: '700px',
        width: '100%',
        padding: '24px',
        backgroundColor: '#f8fafc', // 现代化背景色
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      }}
    >
      <div style={{ marginBottom: '24px' }}>
        <h1
          style={{
            margin: '0 0 16px 0',
            fontSize: '32px',
            fontWeight: 700,
            color: '#111827',
            letterSpacing: '-0.025em',
          }}
        >
          VTable 高性能表格
        </h1>

        {/* 功能特性标签 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flexWrap: 'wrap',
            marginBottom: '16px',
          }}
        >
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: tableData.length > 0 ? '#dbeafe' : '#fef3c7',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: tableData.length > 0 ? '#1e40af' : '#92400e',
            }}
          >
            📊 {tableData.length > 0 ? `${tableData.length.toLocaleString()} 条数据` : '暂无数据'}
          </div>
          {tableData.length > 0 && (
            <div
              style={{
                padding: '6px 12px',
                backgroundColor: selectedRowKeys.length > 0 ? '#dcfce7' : '#f3f4f6',
                borderRadius: '20px',
                fontSize: '13px',
                fontWeight: 500,
                color: selectedRowKeys.length > 0 ? '#166534' : '#6b7280',
              }}
            >
              ✅ 已选择 {selectedRowKeys.length} 项
            </div>
          )}
          {tableData.length > 0 && (
            <div
              style={{
                padding: '6px 12px',
                backgroundColor: getSelectionState.isIndeterminate
                  ? '#fef3c7'
                  : getSelectionState.isAllSelected
                    ? '#dcfce7'
                    : '#f3f4f6',
                borderRadius: '20px',
                fontSize: '13px',
                fontWeight: 500,
                color: getSelectionState.isIndeterminate
                  ? '#92400e'
                  : getSelectionState.isAllSelected
                    ? '#166534'
                    : '#6b7280',
              }}
            >
              {getSelectionState.isIndeterminate
                ? '⊟ 部分选中'
                : getSelectionState.isAllSelected
                  ? '☑ 全选'
                  : '☐ 未选'}
            </div>
          )}
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: '#fef3c7',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#92400e',
            }}
          >
            🚀 Canvas渲染
          </div>
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: '#f3e8ff',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#7c3aed',
            }}
          >
            ⚡ 虚拟滚动
          </div>
        </div>

        {/* 功能描述 */}
        <p
          style={{
            margin: '0',
            color: '#6b7280',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
        >
          ✨ 自适应大小 • 多选功能 • 排序功能 • 点击选择 • 表头级联选择 • Checkbox级联
        </p>

        {/* 多选控制区域 */}
        <div
          style={{
            marginBottom: '20px',
            padding: '20px',
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            border: '1px solid #e5e7eb',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          }}
        >
          <div
            style={{
              marginBottom: '16px',
              fontSize: '16px',
              fontWeight: 600,
              color: '#374151',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            🎛️ 操作控制
            {tableData.length > 0 && selectedRowKeys.length > 0 && (
              <span
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#dcfce7',
                  color: '#166534',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                {selectedRowKeys.length} 项已选
              </span>
            )}
            {tableData.length > 0 && (
              <span
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#f0f9ff',
                  color: '#0369a1',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                🔗 级联选择已启用
              </span>
            )}
            {tableData.length === 0 && (
              <span
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#fef3c7',
                  color: '#92400e',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                📭 数据为空
              </span>
            )}
          </div>
          <Space size='middle' wrap>
            {tableData.length > 0 ? (
              // 有数据时显示的操作按钮
              <>
                <Button
                  size='middle'
                  type='primary'
                  onClick={() => handleSelectAll(true)}
                  disabled={getSelectionState.isAllSelected || isLoading}
                  loading={isLoading}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)',
                  }}
                >
                  ✅ 全选
                </Button>
                <Button
                  size='middle'
                  onClick={() => handleSelectAll(false)}
                  disabled={getSelectionState.selectedCount === 0 || isLoading}
                  loading={isLoading}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                  }}
                >
                  ❌ 取消全选
                </Button>
                <Button
                  size='middle'
                  type='dashed'
                  onClick={() => {
                    try {
                      const newData = generateMockData();
                      resetTableData(newData);
                      message.success('重新生成10000条数据完成！');
                    } catch (error) {
                      console.error('重新生成数据失败:', error);
                      message.error('重新生成数据失败，请重试');
                    }
                  }}
                  disabled={isLoading}
                  loading={isLoading}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    borderColor: '#faad14',
                    color: '#faad14',
                  }}
                >
                  🔄 重新生成数据
                </Button>
                <Button
                  size='middle'
                  type='primary'
                  danger
                  onClick={handleClearAllData}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    boxShadow: '0 2px 4px rgba(255, 77, 79, 0.2)',
                  }}
                >
                  🗑️ 清空所有数据
                </Button>
                {selectedRowKeys.length > 0 && (
                  <>
                    <Button
                      size='middle'
                      onClick={clearSelection}
                      style={{
                        borderRadius: '8px',
                        fontWeight: 500,
                        borderColor: '#d9d9d9',
                      }}
                    >
                      🧹 清除选择
                    </Button>
                    <Button
                      size='middle'
                      type='primary'
                      danger
                      onClick={handleBatchDelete}
                      style={{
                        borderRadius: '8px',
                        fontWeight: 500,
                        boxShadow: '0 2px 4px rgba(255, 77, 79, 0.2)',
                      }}
                    >
                      🗑️ 批量删除 ({selectedRowKeys.length})
                    </Button>
                  </>
                )}
              </>
            ) : (
              // 空数据时显示的操作按钮
              <>
                <Button
                  size='middle'
                  type='primary'
                  onClick={() => {
                    try {
                      const newData = generateMockData();
                      resetTableData(newData);
                      message.success('重新生成10000条数据完成！');
                    } catch (error) {
                      console.error('重新生成数据失败:', error);
                      message.error('重新生成数据失败，请重试');
                    }
                  }}
                  disabled={isLoading}
                  loading={isLoading}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)',
                  }}
                >
                  🚀 生成示例数据
                </Button>
                <Button
                  size='middle'
                  type='dashed'
                  disabled
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    opacity: 0.5,
                  }}
                >
                  📁 导入数据 (即将推出)
                </Button>
              </>
            )}
          </Space>
        </div>
      </div>

      <div
        style={{
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          border: '1px solid #e5e7eb',
          overflow: 'hidden', // 确保圆角效果
          height: containerSize.height - 200, // 为标题和控制区域留出空间
          width: '100%',
          position: 'relative',
        }}
      >
        {tableData.length === 0 ? (
          // 空数据状态
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              padding: '40px',
              textAlign: 'center',
            }}
          >
            <div
              style={{
                fontSize: '64px',
                marginBottom: '24px',
                opacity: 0.3,
              }}
            >
              📊
            </div>
            <h3
              style={{
                margin: '0 0 12px 0',
                fontSize: '20px',
                fontWeight: 600,
                color: '#374151',
              }}
            >
              暂无数据
            </h3>
            <p
              style={{
                margin: '0 0 32px 0',
                fontSize: '14px',
                color: '#6b7280',
                lineHeight: '1.5',
                maxWidth: '400px',
              }}
            >
              当前表格中没有数据。您可以点击"重新生成数据"按钮来加载示例数据，或者导入您自己的数据。
            </p>
            <Button
              type='primary'
              size='large'
              onClick={() => {
                try {
                  const newData = generateMockData();
                  resetTableData(newData);
                  message.success('重新生成10000条数据完成！');
                } catch (error) {
                  console.error('重新生成数据失败:', error);
                  message.error('重新生成数据失败，请重试');
                }
              }}
              disabled={isLoading}
              loading={isLoading}
              style={{
                borderRadius: '8px',
                fontWeight: 500,
                height: '40px',
                padding: '0 24px',
                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)',
              }}
            >
              🚀 生成示例数据
            </Button>
          </div>
        ) : (
          // 有数据时显示表格
          <div
            ref={tableContainerRef}
            style={{
              height: '100%',
              width: '100%',
            }}
          />
        )}
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={`📝 编辑记录 - ${editingRecord?.name || ''}`}
        placement='right'
        width={400}
        open={editDrawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
        footer={
          <div
            style={{
              textAlign: 'right',
            }}
          >
            <Space>
              <Button onClick={handleCancelEdit}>取消</Button>
              <Button type='primary' onClick={handleSaveEdit}>
                保存
              </Button>
            </Space>
          </div>
        }
      >
        <Form
          form={form}
          layout='vertical'
          initialValues={{
            name: editingRecord?.name,
            age: editingRecord?.age,
            email: editingRecord?.email,
            department: editingRecord?.department,
          }}
        >
          <Form.Item
            label='姓名'
            name='name'
            rules={[
              {
                required: true,
                message: '请输入姓名',
              },
            ]}
          >
            <Input placeholder='请输入姓名' />
          </Form.Item>

          <Form.Item
            label='年龄'
            name='age'
            rules={[
              {
                required: true,
                message: '请输入年龄',
              },
              {
                validator: (_, value) => {
                  const age = typeof value === 'string' ? parseInt(value, 10) : value;
                  if (isNaN(age) || age < 18 || age > 65) {
                    return Promise.reject(new Error('年龄必须在18-65之间'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input type='number' placeholder='请输入年龄' />
          </Form.Item>

          <Form.Item
            label='邮箱'
            name='email'
            rules={[
              {
                required: true,
                message: '请输入邮箱',
              },
              {
                type: 'email',
                message: '请输入有效的邮箱地址',
              },
            ]}
          >
            <Input placeholder='请输入邮箱' />
          </Form.Item>

          <Form.Item
            label='部门'
            name='department'
            rules={[
              {
                required: true,
                message: '请选择部门',
              },
            ]}
          >
            <Select placeholder='请选择部门'>
              {DEPARTMENTS.map(dept => (
                <Select.Option key={dept} value={dept}>
                  {dept}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 退出编辑确认Modal */}
      <Modal
        title='确认退出编辑'
        open={exitConfirmVisible}
        onOk={handleConfirmExit}
        onCancel={handleCancelExit}
        okText='确认退出'
        cancelText='继续编辑'
        okType='primary'
        centered
        width={400}
      >
        <div style={{ padding: '16px 0' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              marginBottom: '16px',
            }}
          >
            <span
              style={{
                fontSize: '24px',
              }}
            >
              ⚠️
            </span>
            <span
              style={{
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              您有未保存的更改，确定要退出编辑吗？
            </span>
          </div>
          <div
            style={{
              padding: '12px',
              backgroundColor: '#fff7e6',
              border: '1px solid #ffd591',
              borderRadius: '6px',
              fontSize: '14px',
              color: '#d46b08',
            }}
          >
            💡 退出后所有未保存的更改将会丢失，请确认您的操作。
          </div>
        </div>
      </Modal>

      {/* 右键菜单 */}
      {contextMenuVisible && (
        <div
          style={{
            position: 'fixed',
            top: contextMenuPosition.y,
            left: contextMenuPosition.x,
            backgroundColor: '#ffffff',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            minWidth: '120px',
            padding: '4px 0',
          }}
        >
          <div
            style={{
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              color: '#262626',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#f5f5f5';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={handleContextMenuEdit}
          >
            📝 编辑
          </div>
          <div
            style={{
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              color: '#1890ff',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#f0f9ff';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={handleContextMenuCopy}
          >
            📋 复制
          </div>
          <div
            style={{
              padding: '8px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              color: '#ff4d4f',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = '#fff2f0';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={handleContextMenuDelete}
          >
            🗑️ 删除
          </div>
        </div>
      )}

      {/* 点击其他地方关闭右键菜单 */}
      {contextMenuVisible && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999,
          }}
          onClick={handleCloseContextMenu}
        />
      )}

      {/* 删除确认Modal */}
      <Modal
        title='确认删除'
        open={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText='确认删除'
        cancelText='取消'
        okType='danger'
        centered
        width={400}
      >
        <div style={{ padding: '16px 0' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              marginBottom: '16px',
            }}
          >
            <span
              style={{
                fontSize: '24px',
              }}
            >
              ⚠️
            </span>
            <span
              style={{
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              确定要删除以下记录吗？
            </span>
          </div>
          {deleteRecord && (
            <div
              style={{
                backgroundColor: '#f5f5f5',
                padding: '12px',
                borderRadius: '6px',
                border: '1px solid #d9d9d9',
              }}
            >
              <div
                style={{
                  marginBottom: '8px',
                }}
              >
                <strong>姓名：</strong>
                {deleteRecord.name}
              </div>
              <div
                style={{
                  marginBottom: '8px',
                }}
              >
                <strong>邮箱：</strong>
                {deleteRecord.email}
              </div>
              <div>
                <strong>部门：</strong>
                {deleteRecord.department}
              </div>
            </div>
          )}
          <div
            style={{
              marginTop: '16px',
              padding: '8px 12px',
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: '6px',
              fontSize: '14px',
              color: '#cf1322',
            }}
          >
            ⚠️ 此操作不可撤销，请谨慎操作！
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default VTable;
