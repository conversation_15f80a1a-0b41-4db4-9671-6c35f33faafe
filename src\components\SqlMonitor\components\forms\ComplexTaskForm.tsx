import React, { useState } from 'react';
import { Form, Tabs, Button } from 'antd';
import { SettingOutlined, AlertOutlined, DatabaseOutlined, NotificationOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';

// 导入重构后的模块
import type { TaskBasic, TaskAlert } from '../../types';
import { FORM_BUTTON_TEXT } from '../../constants';
import { formStyles } from '../../styles';
import { DbConnectionModal, AlertSendModal } from '../modals/TaskFormModals';
import { AlertDrawer } from '../drawers/AlertDrawer';
import { OtherInfoModal, SelectModal } from '../modals/TaskFormModalsExtended';

// 导入重构后的子组件
import BasicInfoForm from './BasicInfoForm';
import AlertConfigTab from '../tabs/AlertConfigTab';
import DatabaseConfigTab from '../tabs/DatabaseConfigTab';
import NotificationConfigTab from '../tabs/NotificationConfigTab';
import OtherInfoConfigTab from '../tabs/OtherInfoConfigTab';

// 导入自定义 hooks
import { useFormData, useModalStates, useAvailableData, useFormSubmit } from '../../hooks';

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
}

/**
 * 复合任务表单组件
 * 支持多标签页展示不同类型的配置
 */
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ initialData, onSubmit, onCancel, onReset }) => {
  const [activeTab, setActiveTab] = useState('basic');

  // 使用自定义 hooks 管理状态和逻辑
  const { form, isEditMode, alerts, alertSends, dbConnection, otherInfo, setAlerts, setAlertSends, setDbConnection, setOtherInfo, resetFormData } = useFormData({ initialData });

  const { alertModal, dbConnectionModal, alertSendModal, otherInfoModal, selectModal, setAlertModal, setDbConnectionModal, setAlertSendModal, setOtherInfoModal, setSelectModal } =
    useModalStates();

  const { availableAlerts, availableDbConnections, availableAlertSends, availableOtherInfos } = useAvailableData();

  const { submitLoading, handleFormSubmit } = useFormSubmit({
    form,
    isEditMode,
    initialData,
    alerts,
    alertSends,
    dbConnection,
    otherInfo,
    onSubmit,
  });

  // 重置表单
  const handleReset = () => {
    resetFormData();
    onReset?.();
  };

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: (
        <span>
          <SettingOutlined />
          <span style={{ marginLeft: 8 }}>基本信息</span>
        </span>
      ),
      children: <BasicInfoForm form={form} />,
    },
    {
      key: 'alert',
      label: (
        <span>
          <AlertOutlined />
          <span style={{ marginLeft: 8 }}>告警配置</span>
        </span>
      ),
      children: (
        <AlertConfigTab
          alerts={alerts}
          onAlertsChange={setAlerts}
          onAddAlert={() => setAlertModal({ visible: true, editingIndex: -1 })}
          onEditAlert={index => setAlertModal({ visible: true, editingIndex: index })}
          onSelectAlert={() => setSelectModal({ visible: true, type: 'alert', multiple: true })}
        />
      ),
    },
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          <span style={{ marginLeft: 8 }}>数据库连接</span>
        </span>
      ),
      children: (
        <DatabaseConfigTab
          dbConnection={dbConnection}
          onDbConnectionChange={setDbConnection}
          onAddDbConnection={() => setDbConnectionModal({ visible: true, editingIndex: -1 })}
          onEditDbConnection={() => setDbConnectionModal({ visible: true, editingIndex: -1 })}
          onSelectDbConnection={() =>
            setSelectModal({
              visible: true,
              type: 'dbConnection',
              multiple: false,
            })
          }
        />
      ),
    },
    {
      key: 'notification',
      label: (
        <span>
          <NotificationOutlined />
          <span style={{ marginLeft: 8 }}>告警发送</span>
        </span>
      ),
      children: (
        <NotificationConfigTab
          alertSends={alertSends}
          onAlertSendsChange={setAlertSends}
          onAddAlertSend={() => setAlertSendModal({ visible: true, editingIndex: -1 })}
          onEditAlertSend={index => setAlertSendModal({ visible: true, editingIndex: index })}
          onSelectAlertSend={() => setSelectModal({ visible: true, type: 'alertSend', multiple: true })}
        />
      ),
    },
    {
      key: 'other',
      label: (
        <span>
          <InfoCircleOutlined />
          <span style={{ marginLeft: 8 }}>其他信息</span>
        </span>
      ),
      children: (
        <OtherInfoConfigTab
          otherInfo={otherInfo}
          onOtherInfoChange={setOtherInfo}
          onAddOtherInfo={() => setOtherInfoModal({ visible: true, editingIndex: -1 })}
          onEditOtherInfo={() => setOtherInfoModal({ visible: true, editingIndex: -1 })}
          onSelectOtherInfo={() =>
            setSelectModal({
              visible: true,
              type: 'otherInfo',
              multiple: false,
            })
          }
        />
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <Form form={form} layout="vertical" className="flex-1 overflow-hidden">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className={`${formStyles.formTabs} h-full`}
          style={{ height: '100%' }}
          tabBarStyle={{
            marginBottom: 0,
            paddingLeft: 16,
            paddingRight: 16,
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa',
          }}
        />
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{isEditMode ? '编辑任务信息' : '创建新任务'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            <Button
              onClick={() => {
                handleReset();
                onReset?.();
              }}
              className={`${formStyles.actionButton} ${formStyles.resetButton}`}
            >
              {FORM_BUTTON_TEXT.reset}
            </Button>
            <Button
              type="primary"
              loading={submitLoading}
              onClick={handleFormSubmit}
              className={`${formStyles.actionButton} ${isEditMode ? formStyles.confirmButton : formStyles.submitButton}`}
            >
              {isEditMode ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>

      {/* 告警抽屉 */}
      <AlertDrawer
        visible={alertModal.visible}
        editingData={alertModal.editingIndex !== undefined && alertModal.editingIndex >= 0 ? alerts[alertModal.editingIndex] : undefined}
        onCancel={() => setAlertModal({ visible: false, editingIndex: -1 })}
        onSubmit={(data: TaskAlert) => {
          if (alertModal.editingIndex !== undefined && alertModal.editingIndex >= 0) {
            // 编辑
            const newAlerts = [...alerts];
            newAlerts[alertModal.editingIndex] = data;
            setAlerts(newAlerts);
          } else {
            // 新增
            setAlerts([...alerts, data]);
          }
          setAlertModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 数据库连接Modal */}
      <DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection || undefined}
        onCancel={() => setDbConnectionModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          setDbConnection(data);
          setDbConnectionModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 告警发送Modal */}
      <AlertSendModal
        visible={alertSendModal.visible}
        editingData={alertSendModal.editingIndex !== undefined && alertSendModal.editingIndex >= 0 ? alertSends[alertSendModal.editingIndex] : undefined}
        onCancel={() => setAlertSendModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          if (alertSendModal.editingIndex !== undefined && alertSendModal.editingIndex >= 0) {
            // 编辑
            const newAlertSends = [...alertSends];
            newAlertSends[alertSendModal.editingIndex] = data;
            setAlertSends(newAlertSends);
          } else {
            // 新增
            setAlertSends([...alertSends, data]);
          }
          setAlertSendModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 其他信息Modal */}
      <OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo || undefined}
        onCancel={() => setOtherInfoModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          setOtherInfo(data);
          setOtherInfoModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 选择Modal */}
      <SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={
          selectModal.type === 'alert'
            ? availableAlerts
            : selectModal.type === 'alertSend'
              ? availableAlertSends
              : selectModal.type === 'dbConnection'
                ? availableDbConnections
                : availableOtherInfos
        }
        multiple={selectModal.multiple}
        onCancel={() => setSelectModal({ visible: false, type: 'alert', multiple: false })}
        onSubmit={selectedItems => {
          if (selectModal.type === 'alert') {
            setAlerts([...alerts, ...selectedItems]);
          } else if (selectModal.type === 'alertSend') {
            setAlertSends([...alertSends, ...selectedItems]);
          } else if (selectModal.type === 'dbConnection' && selectedItems.length > 0) {
            setDbConnection(selectedItems[0]);
          } else if (selectModal.type === 'otherInfo' && selectedItems.length > 0) {
            setOtherInfo(selectedItems[0]);
          }
          setSelectModal({ visible: false, type: 'alert', multiple: false });
        }}
      />
    </div>
  );
};

export default ComplexTaskForm;
