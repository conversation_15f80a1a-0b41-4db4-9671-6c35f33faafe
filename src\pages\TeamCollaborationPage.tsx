import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Avatar,
  List,
  Tag,
  Button,
  Space,
  Timeline,
  Progress,
  Tabs,
  Badge,
  Tooltip,
  Empty,
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  MessageOutlined,
  FileTextOutlined,
} from '@ant-design/icons';

const { TabPane } = Tabs;

interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  status: 'online' | 'offline' | 'busy';
  tasksCount: number;
}

interface Project {
  id: string;
  name: string;
  progress: number;
  status: 'active' | 'completed' | 'pending';
  members: string[];
  deadline: string;
}

interface Activity {
  id: string;
  user: string;
  action: string;
  target: string;
  time: string;
  type: 'task' | 'comment' | 'file' | 'meeting';
}

const TeamCollaborationPage: React.FC = () => {
  // 团队成员数据
  const [teamMembers] = useState<TeamMember[]>([
    {
      id: '1',
      name: '张三',
      role: '项目经理',
      status: 'online',
      tasksCount: 5,
    },
    {
      id: '2',
      name: '李四',
      role: '前端开发',
      status: 'online',
      tasksCount: 8,
    },
    {
      id: '3',
      name: '王五',
      role: '后端开发',
      status: 'busy',
      tasksCount: 6,
    },
    {
      id: '4',
      name: '赵六',
      role: 'UI设计师',
      status: 'offline',
      tasksCount: 3,
    },
    {
      id: '5',
      name: '钱七',
      role: '测试工程师',
      status: 'online',
      tasksCount: 4,
    },
  ]);

  // 项目数据
  const [projects] = useState<Project[]>([
    {
      id: '1',
      name: 'React 管理系统',
      progress: 75,
      status: 'active',
      members: ['1', '2', '3'],
      deadline: '2024-09-15',
    },
    {
      id: '2',
      name: '移动端 App',
      progress: 45,
      status: 'active',
      members: ['2', '4', '5'],
      deadline: '2024-10-20',
    },
    {
      id: '3',
      name: 'API 重构',
      progress: 100,
      status: 'completed',
      members: ['1', '3'],
      deadline: '2024-08-01',
    },
  ]);

  // 活动数据
  const [activities] = useState<Activity[]>([
    {
      id: '1',
      user: '李四',
      action: '完成了任务',
      target: '用户登录页面开发',
      time: '2分钟前',
      type: 'task',
    },
    {
      id: '2',
      user: '王五',
      action: '评论了',
      target: 'API 接口设计文档',
      time: '15分钟前',
      type: 'comment',
    },
    {
      id: '3',
      user: '赵六',
      action: '上传了文件',
      target: '界面设计稿 v2.0',
      time: '1小时前',
      type: 'file',
    },
    {
      id: '4',
      user: '张三',
      action: '创建了会议',
      target: '周例会 - 项目进度讨论',
      time: '2小时前',
      type: 'meeting',
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return '#52c41a';
      case 'busy':
        return '#faad14';
      case 'offline':
        return '#d9d9d9';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'busy':
        return '忙碌';
      case 'offline':
        return '离线';
      default:
        return '未知';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'task':
        return (
          <CheckCircleOutlined
            style={{
              color: '#52c41a',
            }}
          />
        );
      case 'comment':
        return (
          <MessageOutlined
            style={{
              color: '#1890ff',
            }}
          />
        );
      case 'file':
        return (
          <FileTextOutlined
            style={{
              color: '#722ed1',
            }}
          />
        );
      case 'meeting':
        return (
          <TeamOutlined
            style={{
              color: '#fa8c16',
            }}
          />
        );
      default:
        return <ClockCircleOutlined />;
    }
  };

  return (
    <div>
      {/* <h1 style={{ marginBottom: 24, fontSize: 24, fontWeight: 600 }}>团队协作</h1> */}

      <Row gutter={[16, 16]}>
        {/* 团队成员 */}
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <TeamOutlined />
                团队成员
                <Badge
                  count={teamMembers.length}
                  style={{
                    backgroundColor: '#52c41a',
                  }}
                />
              </Space>
            }
            extra={
              <Button type='primary' size='small' icon={<PlusOutlined />}>
                邀请成员
              </Button>
            }
            style={{ height: 400 }}
          >
            <List
              dataSource={teamMembers}
              renderItem={member => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Badge dot color={getStatusColor(member.status)} offset={[-5, 5]}>
                        <Avatar icon={<UserOutlined />} />
                      </Badge>
                    }
                    title={
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <span>{member.name}</span>
                        <Tooltip title={getStatusText(member.status)}>
                          <Tag color={getStatusColor(member.status)} size='small'>
                            {getStatusText(member.status)}
                          </Tag>
                        </Tooltip>
                      </div>
                    }
                    description={
                      <div>
                        <div>{member.role}</div>
                        <div
                          style={{
                            fontSize: 12,
                            color: '#666',
                          }}
                        >
                          进行中任务: {member.tasksCount}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 项目进度 */}
        <Col xs={24} lg={8}>
          <Card
            title='项目进度'
            extra={
              <Button type='primary' size='small' icon={<PlusOutlined />}>
                新建项目
              </Button>
            }
            style={{ height: 400 }}
          >
            <List
              dataSource={projects}
              renderItem={project => (
                <List.Item>
                  <div
                    style={{
                      width: '100%',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginBottom: 8,
                      }}
                    >
                      <span
                        style={{
                          fontWeight: 500,
                        }}
                      >
                        {project.name}
                      </span>
                      <Tag
                        color={
                          project.status === 'completed'
                            ? 'green'
                            : project.status === 'active'
                              ? 'blue'
                              : 'orange'
                        }
                      >
                        {project.status === 'completed'
                          ? '已完成'
                          : project.status === 'active'
                            ? '进行中'
                            : '待开始'}
                      </Tag>
                    </div>
                    <Progress
                      percent={project.progress}
                      size='small'
                      status={project.status === 'completed' ? 'success' : 'active'}
                    />
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        marginTop: 8,
                        fontSize: 12,
                        color: '#666',
                      }}
                    >
                      <span>成员: {project.members.length}</span>
                      <span>截止: {project.deadline}</span>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={8}>
          <Card title='最近活动' style={{ height: 400 }}>
            <Timeline
              items={activities.map(activity => ({
                key: activity.id,
                dot: getActivityIcon(activity.type),
                children: (
                  <div>
                    <div
                      style={{
                        fontSize: 14,
                      }}
                    >
                      <strong>{activity.user}</strong> {activity.action}{' '}
                      <span
                        style={{
                          color: '#1890ff',
                        }}
                      >
                        {activity.target}
                      </span>
                    </div>
                    <div
                      style={{
                        fontSize: 12,
                        color: '#666',
                        marginTop: 4,
                      }}
                    >
                      {activity.time}
                    </div>
                  </div>
                ),
              }))}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Card style={{ marginTop: 16 }}>
        <Tabs defaultActiveKey='1'>
          <TabPane tab='项目看板' key='1'>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Card title='待办' size='small'>
                  <Empty description='暂无待办任务' />
                </Card>
              </Col>
              <Col span={8}>
                <Card title='进行中' size='small'>
                  <List
                    size='small'
                    dataSource={[
                      {
                        title: '用户界面优化',
                        assignee: '李四',
                      },
                      {
                        title: 'API 接口开发',
                        assignee: '王五',
                      },
                    ]}
                    renderItem={item => (
                      <List.Item>
                        <div>
                          <div>{item.title}</div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#666',
                            }}
                          >
                            负责人: {item.assignee}
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title='已完成' size='small'>
                  <List
                    size='small'
                    dataSource={[
                      {
                        title: '登录功能开发',
                        assignee: '李四',
                      },
                      {
                        title: '数据库设计',
                        assignee: '王五',
                      },
                    ]}
                    renderItem={item => (
                      <List.Item>
                        <div>
                          <div>{item.title}</div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#666',
                            }}
                          >
                            负责人: {item.assignee}
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab='团队日历' key='2'>
            <Empty description='团队日历功能开发中...' />
          </TabPane>
          <TabPane tab='文件共享' key='3'>
            <Empty description='文件共享功能开发中...' />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TeamCollaborationPage;
