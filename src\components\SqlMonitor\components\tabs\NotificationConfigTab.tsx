import React from 'react';
import { Card, Button, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
  onAddAlertSend: () => void;
  onEditAlertSend: (index: number) => void;
  onSelectAlertSend: () => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({
  alertSends,
  onAlertSendsChange,
  onAddAlertSend,
  onEditAlertSend,
  onSelectAlertSend,
}) => {
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = alertSends.filter((_, i) => i !== index);
    onAlertSendsChange(newAlertSends);
  };

  const columns: ColumnsType<AlertSend> = [
    {
      title: '发送名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '发送类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <span
          style={{
            color: type === 'kafka' ? '#1890ff' : '#52c41a',
            fontWeight: 'bold',
          }}
        >
          {type.toUpperCase()}
        </span>
      ),
    },
    {
      title: '配置信息',
      key: 'config',
      ellipsis: true,
      render: (_: string, record: AlertSend) => {
        const config = record.type === 'kafka' ? record.kafka_receiver : record.prometheus_receiver;
        return <span style={{ fontSize: '12px' }}>{config.address}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: string, _record: AlertSend, index: number) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EditOutlined />}
            onClick={() => onEditAlertSend(index)}
          >
            编辑
          </Button>
          <Button
            type='text'
            size='small'
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className={formStyles.tabContent}>
      <Card
        title='告警发送配置'
        size='small'
        className='mb-4'
        extra={
          <Space>
            <Button type='primary' size='small' icon={<PlusOutlined />} onClick={onAddAlertSend}>
              新增发送
            </Button>
            <Button size='small' icon={<EyeOutlined />} onClick={onSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        }
      >
        <Table
          dataSource={alertSends}
          rowKey='id'
          size='small'
          pagination={false}
          columns={columns}
        />
        {alertSends.length === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            暂无告警发送配置，请点击"新增发送"或"选择发送"添加
          </div>
        )}
      </Card>
    </div>
  );
};

export default NotificationConfigTab;
