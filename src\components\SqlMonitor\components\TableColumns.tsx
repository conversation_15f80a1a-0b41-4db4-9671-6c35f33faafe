import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space, Tag } from 'antd';
import type { FilterDropdownProps } from 'antd/es/table/interface';
import React from 'react';

import type { TaskBasic } from '../types';
import { TABLE_COLUMN_WIDTHS } from '../constants';
import { tableStyles } from '../styles';

interface TableColumnsProps {
  filteredInfo: Record<string, any>;
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  onEdit: (record: TaskBasic) => void;
  onDelete: (id: number) => void;
}

/**
 * 自定义筛选下拉组件
 */
const getColumnSearchProps = (dataIndex: string, placeholder: string, filteredInfo: Record<string, any>) => ({
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
    <div style={{ padding: 8 }}>
      <Input
        placeholder={`搜索 ${placeholder}`}
        value={selectedKeys[0]}
        onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
        onPressEnter={() => confirm()}
        style={{ marginBottom: 8, display: 'block' }}
      />
      <Space>
        <Button type="primary" onClick={() => confirm()} icon={<SearchOutlined />} size="small" style={{ width: 90 }}>
          搜索
        </Button>
        <Button
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          }}
          size="small"
          style={{ width: 90 }}
        >
          重置
        </Button>
      </Space>
    </div>
  ),
  filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  onFilter: (value: React.Key | boolean, record: TaskBasic) => record[dataIndex as keyof TaskBasic]?.toString().toLowerCase().includes(value.toString().toLowerCase()),
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 获取选择筛选器配置
 */
const getColumnSelectProps = (dataIndex: string, options: { text: string; value: string }[], filteredInfo: Record<string, any>) => ({
  filters: options,
  onFilter: (value: React.Key | boolean, record: TaskBasic) => {
    const fieldValue = record[dataIndex as keyof TaskBasic];
    if (dataIndex === 'status') {
      return fieldValue === value;
    }
    return fieldValue?.toString().includes(value.toString());
  },
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 创建表格列配置
 */
export const createTableColumns = ({ filteredInfo, getSortOrder, onEdit, onDelete }: TableColumnsProps): TableColumnsType<TaskBasic> => [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name',
    width: TABLE_COLUMN_WIDTHS.name,
    ellipsis: true,
    fixed: 'left',
    sorter: (a, b) => a.name.localeCompare(b.name),
    sortOrder: getSortOrder('name'),
    ...getColumnSearchProps('name', '任务名称', filteredInfo),
  },
  {
    title: '任务分组',
    dataIndex: 'group_name',
    key: 'group_name',
    width: TABLE_COLUMN_WIDTHS.group_name,
    sorter: (a, b) => a.group_name.localeCompare(b.group_name),
    sortOrder: getSortOrder('group_name'),
    ...getColumnSearchProps('group_name', '任务分组', filteredInfo),
  },
  {
    title: '执行时间',
    key: 'time',
    width: TABLE_COLUMN_WIDTHS.time,
    sorter: (a, b) => a.start_time.localeCompare(b.start_time),
    sortOrder: getSortOrder('start_time'),
    filteredValue: null,
    render: (_, record) => (
      <span>
        {record.start_time} - {record.end_time}
      </span>
    ),
  },
  {
    title: '星期',
    dataIndex: 'weekday',
    key: 'weekday',
    width: TABLE_COLUMN_WIDTHS.weekday,
    sorter: (a, b) => {
      const aWeekday = Array.isArray(a.weekday) ? a.weekday.join(',') : a.weekday;
      const bWeekday = Array.isArray(b.weekday) ? b.weekday.join(',') : b.weekday;
      return aWeekday.localeCompare(bWeekday);
    },
    sortOrder: getSortOrder('weekday'),
    render: (weekday: string | string[]) => {
      if (Array.isArray(weekday)) {
        return weekday.join(',');
      }
      return weekday;
    },
  },
  {
    title: '执行频率',
    dataIndex: 'frequency',
    key: 'frequency',
    width: TABLE_COLUMN_WIDTHS.frequency,
    sorter: (a, b) => {
      const aFreq = typeof a.frequency === 'object' ? `${a.frequency.value}${a.frequency.unit}` : a.frequency;
      const bFreq = typeof b.frequency === 'object' ? `${b.frequency.value}${b.frequency.unit}` : b.frequency;
      return aFreq.toString().localeCompare(bFreq.toString());
    },
    sortOrder: getSortOrder('frequency'),
    filteredValue: null,
    render: (frequency: string | { value: number; unit: string }) => {
      if (frequency && typeof frequency === 'object') {
        return `${frequency.value}${frequency.unit}`;
      }
      return frequency;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: TABLE_COLUMN_WIDTHS.status,
    sorter: (a, b) => a.status.localeCompare(b.status),
    sortOrder: getSortOrder('status'),
    ...getColumnSelectProps(
      'status',
      [
        { text: '启用', value: 'enabled' },
        { text: '禁用', value: 'disabled' },
      ],
      filteredInfo
    ),
    render: (status: string) => <Tag color={status === 'enabled' ? 'success' : 'error'}>{status === 'enabled' ? '启用' : '禁用'}</Tag>,
  },
  {
    title: '重试次数',
    dataIndex: 'retry_num',
    key: 'retry_num',
    width: TABLE_COLUMN_WIDTHS.retry_num,
    sorter: (a, b) => parseInt(a.retry_num) - parseInt(b.retry_num),
    sortOrder: getSortOrder('retry_num'),
  },
  {
    title: '重试间隔',
    dataIndex: 'retry_frequency',
    key: 'retry_frequency',
    width: TABLE_COLUMN_WIDTHS.retry_frequency,
    sorter: (a, b) => {
      const aRetryFreq = typeof a.retry_frequency === 'object' ? `${a.retry_frequency.value}${a.retry_frequency.unit}` : a.retry_frequency;
      const bRetryFreq = typeof b.retry_frequency === 'object' ? `${b.retry_frequency.value}${b.retry_frequency.unit}` : b.retry_frequency;
      return aRetryFreq.toString().localeCompare(bRetryFreq.toString());
    },
    sortOrder: getSortOrder('retry_frequency'),
    filteredValue: null,
    render: (retry_frequency: string | { value: number; unit: string }) => {
      if (retry_frequency && typeof retry_frequency === 'object') {
        return `${retry_frequency.value}${retry_frequency.unit}`;
      }
      return retry_frequency;
    },
  },
  {
    title: '操作',
    key: 'action',
    width: TABLE_COLUMN_WIDTHS.action,
    fixed: 'right',
    filteredValue: null,
    render: (_, record) => (
      <Space size="small">
        <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEdit(record)} className={tableStyles.editButton}>
          编辑
        </Button>
        <Popconfirm title="确认删除" description="确定要删除这个任务吗？" onConfirm={() => onDelete(record.id)} okText="确定" cancelText="取消" placement="topRight">
          <Button type="text" size="small" danger icon={<DeleteOutlined />} className={tableStyles.deleteButton}>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  },
];
