/**
 * HTTP客户端服务
 * 基于项目现有的httpClient进行封装
 */

import { httpClient as baseHttpClient } from '../../../services/http/client';
import type { ApiResponse } from '../types';

/**
 * SqlMonitor专用HTTP客户端
 * 封装了通用的请求方法和错误处理
 */
export class SqlMonitorHttpClient {
  /**
   * GET请求
   */
  static async get<T>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      const response = await baseHttpClient.get<ApiResponse<T>>(url, { params });
      return response;
    } catch (error) {
      console.error('GET请求失败:', error);
      throw error;
    }
  }

  /**
   * POST请求
   */
  static async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await baseHttpClient.post<ApiResponse<T>>(url, data);
      return response;
    } catch (error) {
      console.error('POST请求失败:', error);
      throw error;
    }
  }

  /**
   * PUT请求
   */
  static async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await baseHttpClient.put<ApiResponse<T>>(url, data);
      return response;
    } catch (error) {
      console.error('PUT请求失败:', error);
      throw error;
    }
  }

  /**
   * DELETE请求
   */
  static async delete<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await baseHttpClient.delete<ApiResponse<T>>(url);
      return response;
    } catch (error) {
      console.error('DELETE请求失败:', error);
      throw error;
    }
  }

  /**
   * 模拟延迟（用于开发测试）
   */
  static async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 导出简化的别名
export const httpClient = SqlMonitorHttpClient;
