import React from 'react';
import { Tabs } from 'antd';
import type { TabsContainerProps } from '../types';
import styles from '../DefaultLayout.module.css';

/**
 * 标签页容器组件
 */
export const TabsContainer: React.FC<TabsContainerProps> = ({
  activeTabKey,
  tabs,
  onTabChange,
  onTabRemove,
}) => {
  return (
    <div className={styles.tabsContainer}>
      <Tabs
        type='editable-card'
        activeKey={activeTabKey}
        onChange={onTabChange}
        onEdit={(targetKey, action) => {
          if (action === 'remove') {
            onTabRemove(targetKey as string);
          }
        }}
        size='small'
        className={styles.customTabs}
        items={tabs.map(tab => ({
          key: tab.key,
          label: tab.label,
          closable: tab.closable,
        }))}
      />
    </div>
  );
};
